/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
SwiftUI image loader with caching, progressive loading, and error handling.
*/

import Foundation
import SwiftUI
import Combine
#if os(iOS)
import UIKit
typealias PlatformImage = UIImage
#elseif os(macOS)
import AppKit
typealias PlatformImage = NSImage
#endif

/// SwiftUI image loader with caching capabilities
@MainActor
final class ImageLoader: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var image: PlatformImage?
    @Published var isLoading: Bool = false
    @Published var error: AppError?
    @Published var progress: Double = 0.0
    
    // MARK: - Private Properties
    
    private let imageCache: ImageCache
    private let networkService: NetworkServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    private var loadingTask: Task<Void, Never>?
    
    // MARK: - Configuration
    
    private let configuration: ImageLoaderConfiguration
    
    // MARK: - Initialization
    
    init(
        imageCache: ImageCache = .shared,
        networkService: NetworkServiceProtocol = NetworkService(),
        configuration: ImageLoaderConfiguration = .default
    ) {
        self.imageCache = imageCache
        self.networkService = networkService
        self.configuration = configuration
    }
    
    deinit {
        cancel()
    }
    
    // MARK: - Public Methods
    
    /// Loads an image from the given source
    func load(_ source: ImageSource) {
        // Cancel any existing loading operation
        cancel()
        
        // Reset state
        resetState()
        
        // Check cache first
        if let cachedImage = getCachedImage(for: source) {
            self.image = cachedImage
            return
        }
        
        // Start loading
        isLoading = true
        
        loadingTask = Task { @MainActor in
            do {
                let loadedImage = try await loadImage(from: source)
                
                // Cache the loaded image
                imageCache.store(loadedImage, for: source.cacheKey)
                
                // Update UI
                self.image = loadedImage
                self.isLoading = false
                self.progress = 1.0
                
            } catch {
                // Handle error
                self.error = AppError.from(error)
                self.isLoading = false
                self.progress = 0.0
                
                // Set placeholder if configured
                if let placeholder = configuration.errorPlaceholder {
                    self.image = placeholder
                }
            }
        }
    }
    
    /// Cancels the current loading operation
    func cancel() {
        loadingTask?.cancel()
        loadingTask = nil
        isLoading = false
        progress = 0.0
    }
    
    /// Retries loading the last failed image
    func retry() {
        guard let lastSource = getLastSource() else { return }
        load(lastSource)
    }
    
    // MARK: - Private Methods
    
    private func resetState() {
        image = nil
        error = nil
        progress = 0.0
        
        // Set loading placeholder if configured
        if let placeholder = configuration.loadingPlaceholder {
            image = placeholder
        }
    }
    
    private func getCachedImage(for source: ImageSource) -> PlatformImage? {
        return imageCache.image(for: source.cacheKey)
    }
    
    private func loadImage(from source: ImageSource) async throws -> PlatformImage {
        switch source {
        case .local(let imageName):
            return try loadLocalImage(named: imageName)
        case .remote(let url):
            return try await loadRemoteImage(from: url)
        case .data(let data):
            return try loadImageFromData(data)
        }
    }
    
    private func loadLocalImage(named imageName: String) throws -> PlatformImage {
        guard let image = PlatformImage(named: imageName) else {
            throw ImageLoaderError.imageNotFound(imageName)
        }
        return image
    }
    
    private func loadRemoteImage(from url: URL) async throws -> PlatformImage {
        // Create progress tracking
        let progressSubject = PassthroughSubject<Double, Never>()
        progressSubject
            .receive(on: DispatchQueue.main)
            .sink { [weak self] progress in
                self?.progress = progress
            }
            .store(in: &cancellables)
        
        // Download image data
        let data = try await networkService.downloadData(from: url)
        
        // Create image from data
        guard let image = PlatformImage(data: data) else {
            throw ImageLoaderError.invalidImageData
        }
        
        // Apply transformations if needed
        return try applyTransformations(to: image)
    }
    
    private func loadImageFromData(_ data: Data) throws -> PlatformImage {
        guard let image = PlatformImage(data: data) else {
            throw ImageLoaderError.invalidImageData
        }
        
        return try applyTransformations(to: image)
    }
    
    private func applyTransformations(to image: PlatformImage) throws -> PlatformImage {
        var processedImage = image
        
        // Apply resizing if configured
        if let targetSize = configuration.targetSize {
            processedImage = try resizeImage(processedImage, to: targetSize)
        }
        
        // Apply compression if configured
        if let compressionQuality = configuration.compressionQuality {
            processedImage = try compressImage(processedImage, quality: compressionQuality)
        }
        
        return processedImage
    }
    
    private func resizeImage(_ image: PlatformImage, to targetSize: CGSize) throws -> PlatformImage {
#if os(iOS)
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        
        let resizedImage = renderer.image { context in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
        
        return resizedImage
#elseif os(macOS)
        let resizedImage = NSImage(size: targetSize)
        resizedImage.lockFocus()
        image.draw(in: CGRect(origin: .zero, size: targetSize))
        resizedImage.unlockFocus()
        return resizedImage
#endif
    }
    
    private func compressImage(_ image: PlatformImage, quality: CGFloat) throws -> PlatformImage {
#if os(iOS)
        guard let data = image.jpegData(compressionQuality: quality),
              let compressedImage = UIImage(data: data) else {
            throw ImageLoaderError.compressionFailed
        }
        
        return compressedImage
#elseif os(macOS)
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil),
              let bitmapRep = NSBitmapImageRep(cgImage: cgImage),
              let data = bitmapRep.representation(using: .jpeg, properties: [.compressionFactor: quality]),
              let compressedImage = NSImage(data: data) else {
            throw ImageLoaderError.compressionFailed
        }
        
        return compressedImage
#endif
    }
    
    private func getLastSource() -> ImageSource? {
        // In a real implementation, you'd store the last source
        // For now, return nil
        return nil
    }
}

// MARK: - Supporting Types

/// Image source enumeration
enum ImageSource {
    case local(String)
    case remote(URL)
    case data(Data)
    
    var cacheKey: String {
        switch self {
        case .local(let imageName):
            return "local_\(imageName)"
        case .remote(let url):
            return "remote_\(url.absoluteString)"
        case .data(let data):
            return "data_\(data.hashValue)"
        }
    }
}

/// Configuration for image loader
struct ImageLoaderConfiguration {
    let targetSize: CGSize?
    let compressionQuality: CGFloat?
    let loadingPlaceholder: PlatformImage?
    let errorPlaceholder: PlatformImage?
    let enableProgressiveLoading: Bool
    let maxRetryAttempts: Int
    
    static let `default` = ImageLoaderConfiguration(
        targetSize: nil,
        compressionQuality: nil,
        loadingPlaceholder: nil,
        errorPlaceholder: nil,
        enableProgressiveLoading: false,
        maxRetryAttempts: 3
    )
}

/// Image loader errors
enum ImageLoaderError: Error, LocalizedError {
    case imageNotFound(String)
    case invalidImageData
    case compressionFailed
    case resizingFailed
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .imageNotFound(let imageName):
            return "Image not found: \(imageName)"
        case .invalidImageData:
            return "Invalid image data"
        case .compressionFailed:
            return "Image compression failed"
        case .resizingFailed:
            return "Image resizing failed"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - SwiftUI Integration

/// SwiftUI view for cached image loading
struct CachedAsyncImage<Content: View>: View {
    private let source: ImageSource
    private let content: (AsyncImagePhase) -> Content
    
    @StateObject private var loader = ImageLoader()
    
    init(
        _ source: ImageSource,
        @ViewBuilder content: @escaping (AsyncImagePhase) -> Content
    ) {
        self.source = source
        self.content = content
    }
    
    var body: some View {
        content(currentPhase)
            .onAppear {
                loader.load(source)
            }
            .onChange(of: source) { newSource in
                loader.load(newSource)
            }
    }
    
    private var currentPhase: AsyncImagePhase {
        if let image = loader.image {
#if os(iOS)
            return .success(Image(uiImage: image))
#elseif os(macOS)
            return .success(Image(nsImage: image))
#endif
        } else if loader.isLoading {
            return .empty
        } else if let error = loader.error {
            return .failure(error)
        } else {
            return .empty
        }
    }
}

/// Convenience initializers for CachedAsyncImage
extension CachedAsyncImage {
    init(
        _ source: ImageSource
    ) where Content == _ConditionalContent<Image, Color> {
        self.init(source) { phase in
            switch phase {
            case .success(let image):
                image
            case .failure, .empty:
                Color.gray.opacity(0.3)
            @unknown default:
                Color.gray.opacity(0.3)
            }
        }
    }
    
    init<I: View, P: View>(
        _ source: ImageSource,
        @ViewBuilder content: @escaping (Image) -> I,
        @ViewBuilder placeholder: @escaping () -> P
    ) where Content == _ConditionalContent<I, P> {
        self.init(source) { phase in
            switch phase {
            case .success(let image):
                content(image)
            case .failure, .empty:
                placeholder()
            @unknown default:
                placeholder()
            }
        }
    }
}

/// Helper view for displaying loading progress
struct ImageLoadingProgressView: View {
    let progress: Double
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 4)
            
            Circle()
                .trim(from: 0, to: progress)
                .stroke(Color.blue, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut, value: progress)
        }
        .frame(width: 40, height: 40)
    }
}