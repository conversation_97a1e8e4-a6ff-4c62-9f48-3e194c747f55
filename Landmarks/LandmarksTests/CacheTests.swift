/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Unit tests for caching systems including ImageCache and ResponsiveDesignCache.
*/

import XCTest
import UIKit
import Swift<PERSON>
@testable import Landmarks

final class CacheTests: XCTestCase {
    
    var imageCache: ImageCache!
    var responsiveDesignCache: ResponsiveDesignCache!
    
    override func setUpWithError() throws {
        // Create test configuration for image cache
        let config = ImageCacheConfiguration(
            memoryCountLimit: 10,
            memoryCostLimit: 1024 * 1024, // 1MB
            maxDiskCacheSize: 5 * 1024 * 1024, // 5MB
            maxAge: 60, // 1 minute for testing
            cleanupInterval: 10, // 10 seconds for testing
            diskCompressionQuality: 0.8
        )
        imageCache = ImageCache(configuration: config)
        responsiveDesignCache = ResponsiveDesignCache.shared
    }
    
    override func tearDownWithError() throws {
        imageCache.clearCache()
        responsiveDesignCache.clearCache()
        imageCache = nil
        responsiveDesignCache = nil
    }
    
    // MARK: - ImageCache Tests
    
    func testImageCacheStoreAndRetrieve() {
        // Given
        let testImage = createTestImage()
        let testKey = "test_image"
        
        // When
        imageCache.store(testImage, for: testKey)
        let retrievedImage = imageCache.image(for: testKey)
        
        // Then
        XCTAssertNotNil(retrievedImage, "Should retrieve stored image")
        XCTAssertEqual(retrievedImage?.size, testImage.size, "Retrieved image should have same size")
    }
    
    func testImageCacheRetrieveNonExistent() {
        // Given
        let nonExistentKey = "non_existent_key"
        
        // When
        let retrievedImage = imageCache.image(for: nonExistentKey)
        
        // Then
        XCTAssertNil(retrievedImage, "Should return nil for non-existent image")
    }
    
    func testImageCacheRemove() {
        // Given
        let testImage = createTestImage()
        let testKey = "test_remove"
        imageCache.store(testImage, for: testKey)
        
        // When
        imageCache.removeImage(for: testKey)
        let retrievedImage = imageCache.image(for: testKey)
        
        // Then
        XCTAssertNil(retrievedImage, "Should not retrieve removed image")
    }
    
    func testImageCacheClear() {
        // Given
        let testImage1 = createTestImage()
        let testImage2 = createTestImage(size: CGSize(width: 200, height: 200))
        imageCache.store(testImage1, for: "image1")
        imageCache.store(testImage2, for: "image2")
        
        // When
        imageCache.clearCache()
        
        // Then
        XCTAssertNil(imageCache.image(for: "image1"), "Should not retrieve image after clear")
        XCTAssertNil(imageCache.image(for: "image2"), "Should not retrieve image after clear")
    }
    
    func testImageCacheStatistics() async {
        // Given
        let testImage = createTestImage()
        let testKey = "stats_test"
        
        // When
        imageCache.store(testImage, for: testKey)
        _ = imageCache.image(for: testKey) // Memory hit
        _ = imageCache.image(for: "non_existent") // Miss
        
        let stats = imageCache.cacheStatistics
        
        // Then
        XCTAssertGreaterThan(stats.totalRequests, 0, "Should have requests")
        XCTAssertGreaterThan(stats.hitRate, 0, "Should have some hits")
    }
    
    func testImageCacheSize() async {
        // Given
        let testImage = createTestImage()
        imageCache.store(testImage, for: "size_test")
        
        // When
        let sizeInfo = await imageCache.getCacheSize()
        
        // Then
        XCTAssertGreaterThanOrEqual(sizeInfo.totalSize, 0, "Total size should be non-negative")
        XCTAssertNotNil(sizeInfo.formattedTotalSize, "Should have formatted size string")
    }
    
    func testImageCacheConfiguration() {
        // Given
        let config = ImageCacheConfiguration.default
        
        // Then
        XCTAssertGreaterThan(config.memoryCountLimit, 0, "Should have memory count limit")
        XCTAssertGreaterThan(config.memoryCostLimit, 0, "Should have memory cost limit")
        XCTAssertGreaterThan(config.maxDiskCacheSize, 0, "Should have disk cache size limit")
        XCTAssertGreaterThan(config.maxAge, 0, "Should have max age")
        XCTAssertGreaterThan(config.cleanupInterval, 0, "Should have cleanup interval")
        XCTAssertGreaterThan(config.diskCompressionQuality, 0, "Should have compression quality")
        XCTAssertLessThanOrEqual(config.diskCompressionQuality, 1, "Compression quality should be <= 1")
    }
    
    // MARK: - ResponsiveDesignCache Tests
    
    @MainActor
    func testResponsiveDesignCacheDeviceType() {
        // When
        let deviceType1 = responsiveDesignCache.getDeviceType()
        let deviceType2 = responsiveDesignCache.getDeviceType() // Should use cache
        
        // Then
        XCTAssertEqual(deviceType1, deviceType2, "Cached device type should be consistent")
    }
    
    @MainActor
    func testResponsiveDesignCacheScreenSize() {
        // When
        let screenSize1 = responsiveDesignCache.getScreenSize()
        let screenSize2 = responsiveDesignCache.getScreenSize() // Should use cache
        
        // Then
        XCTAssertEqual(screenSize1, screenSize2, "Cached screen size should be consistent")
    }
    
    @MainActor
    func testResponsiveDesignCacheTypographyScale() {
        // When
        let scale1 = responsiveDesignCache.getTypographyScale()
        let scale2 = responsiveDesignCache.getTypographyScale() // Should use cache
        
        // Then
        XCTAssertEqual(scale1.title, scale2.title, "Cached typography scale should be consistent")
        XCTAssertEqual(scale1.body, scale2.body, "Cached typography scale should be consistent")
    }
    
    @MainActor
    func testResponsiveDesignCacheSpacing() {
        // When
        let smallSpacing = responsiveDesignCache.getSpacing(.small)
        let mediumSpacing = responsiveDesignCache.getSpacing(.medium)
        let largeSpacing = responsiveDesignCache.getSpacing(.large)
        
        // Then
        XCTAssertGreaterThan(mediumSpacing, smallSpacing, "Medium spacing should be larger than small")
        XCTAssertGreaterThan(largeSpacing, mediumSpacing, "Large spacing should be larger than medium")
    }
    
    @MainActor
    func testResponsiveDesignCacheScaleFactor() {
        // When
        let titleScale = responsiveDesignCache.getScaleFactor(for: .title)
        let bodyScale = responsiveDesignCache.getScaleFactor(for: .body)
        
        // Then
        XCTAssertGreaterThan(titleScale, 0, "Title scale should be positive")
        XCTAssertGreaterThan(bodyScale, 0, "Body scale should be positive")
    }
    
    @MainActor
    func testResponsiveDesignCacheClear() {
        // Given
        _ = responsiveDesignCache.getDeviceType() // Populate cache
        _ = responsiveDesignCache.getScreenSize() // Populate cache
        
        // When
        responsiveDesignCache.clearCache()
        
        // Then - Should not crash and should be callable
        _ = responsiveDesignCache.getDeviceType() // Should recompute
        XCTAssertTrue(true, "Cache clear should work without issues")
    }
    
    @MainActor
    func testResponsiveDesignCacheRefresh() {
        // Given
        let initialDeviceType = responsiveDesignCache.getDeviceType()
        
        // When
        responsiveDesignCache.refreshCache()
        let refreshedDeviceType = responsiveDesignCache.getDeviceType()
        
        // Then
        XCTAssertEqual(initialDeviceType, refreshedDeviceType, "Device type should remain same after refresh")
    }
    
    @MainActor
    func testResponsiveDesignCacheStats() {
        // Given
        _ = responsiveDesignCache.getDeviceType()
        _ = responsiveDesignCache.getScreenSize()
        
        // When
        let stats = responsiveDesignCache.getCacheStats()
        
        // Then
        XCTAssertTrue(stats.deviceTypeCacheHit, "Should hit device type cache")
        XCTAssertTrue(stats.screenSizeCacheHit, "Should hit screen size cache")
        XCTAssertGreaterThan(stats.cacheEfficiency, 0, "Should have positive cache efficiency")
    }
    
    // MARK: - ImageLoader Tests
    
    func testImageLoaderInitialization() {
        // Given
        let loader = ImageLoader()
        
        // Then
        XCTAssertNil(loader.image, "Should start with no image")
        XCTAssertFalse(loader.isLoading, "Should start not loading")
        XCTAssertNil(loader.error, "Should start with no error")
        XCTAssertEqual(loader.progress, 0.0, "Should start with zero progress")
    }
    
    func testImageLoaderLocalImage() {
        // Given
        let loader = ImageLoader()
        let source = ImageSource.local("test_image") // Assuming this image doesn't exist
        
        // When
        loader.load(source)
        
        // Then
        // Should eventually set an error since the image doesn't exist
        let expectation = XCTestExpectation(description: "Image loading completes")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if loader.error != nil || loader.image != nil || !loader.isLoading {
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testImageLoaderCancel() {
        // Given
        let loader = ImageLoader()
        let source = ImageSource.local("test_image")
        
        // When
        loader.load(source)
        loader.cancel()
        
        // Then
        XCTAssertFalse(loader.isLoading, "Should stop loading after cancel")
        XCTAssertEqual(loader.progress, 0.0, "Should reset progress after cancel")
    }
    
    func testImageLoaderConfiguration() {
        // Given
        let config = ImageLoaderConfiguration.default
        
        // Then
        XCTAssertNil(config.targetSize, "Default should have no target size")
        XCTAssertNil(config.compressionQuality, "Default should have no compression")
        XCTAssertNil(config.loadingPlaceholder, "Default should have no loading placeholder")
        XCTAssertNil(config.errorPlaceholder, "Default should have no error placeholder")
        XCTAssertEqual(config.maxRetryAttempts, 3, "Default should have 3 retry attempts")
    }
    
    // MARK: - Performance Tests
    
    func testImageCachePerformance() {
        let images = (0..<100).map { _ in createTestImage() }
        
        measure {
            for (index, image) in images.enumerated() {
                imageCache.store(image, for: "perf_test_\(index)")
            }
            
            for index in 0..<100 {
                _ = imageCache.image(for: "perf_test_\(index)")
            }
        }
    }
    
    @MainActor
    func testResponsiveDesignCachePerformance() {
        measure {
            for _ in 0..<1000 {
                _ = responsiveDesignCache.getDeviceType()
                _ = responsiveDesignCache.getScreenSize()
                _ = responsiveDesignCache.getTypographyScale()
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func createTestImage(size: CGSize = CGSize(width: 100, height: 100)) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
    }
}