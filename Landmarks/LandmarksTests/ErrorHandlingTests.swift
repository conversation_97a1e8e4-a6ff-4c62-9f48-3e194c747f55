/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Unit tests for error handling system.
*/

import XCTest
@testable import Landmarks

@MainActor
final class ErrorHandlingTests: XCTestCase {
    
    var errorHandler: ErrorHandler!
    
    override func setUpWithError() throws {
        errorHandler = ErrorHandler()
    }
    
    override func tearDownWithError() throws {
        errorHandler = nil
    }
    
    // MARK: - AppError Tests
    
    func testAppErrorIdentifiable() {
        // Given
        let error1 = AppError.dataNotFound("test")
        let error2 = AppError.dataNotFound("test")
        let error3 = AppError.networkUnavailable
        
        // Then
        XCTAssertEqual(error1.id, error2.id, "Same errors should have same ID")
        XCTAssertNotEqual(error1.id, error3.id, "Different errors should have different IDs")
    }
    
    func testAppErrorSeverity() {
        // Given & When
        let lowSeverityError = AppError.operationCancelled
        let mediumSeverityError = AppError.networkTimeout
        let highSeverityError = AppError.dataSavingFailed("test")
        
        // Then
        XCTAssertEqual(lowSeverityError.severity, .low)
        XCTAssertEqual(mediumSeverityError.severity, .medium)
        XCTAssertEqual(highSeverityError.severity, .high)
    }
    
    func testAppErrorUserFriendlyProperties() {
        // Given
        let error = AppError.networkUnavailable
        
        // Then
        XCTAssertNotNil(error.errorDescription, "Should have error description")
        XCTAssertNotNil(error.recoverySuggestion, "Should have recovery suggestion")
        XCTAssertTrue(error.shouldShowToUser, "Network errors should be shown to user")
        
        // Test cancelled operation
        let cancelledError = AppError.operationCancelled
        XCTAssertFalse(cancelledError.shouldShowToUser, "Cancelled operations should not be shown to user")
    }
    
    func testAppErrorEquality() {
        // Given
        let error1 = AppError.dataNotFound("test")
        let error2 = AppError.dataNotFound("test")
        let error3 = AppError.networkUnavailable
        
        // Then
        XCTAssertEqual(error1, error2, "Same errors should be equal")
        XCTAssertNotEqual(error1, error3, "Different errors should not be equal")
    }
    
    func testAppErrorConversion() {
        // Given
        let networkError = NetworkServiceError.noConnection
        let mapError = MapServiceError.missingPlaceID
        let collectionError = CollectionRepositoryError.favoritesCollectionNotFound
        let preferencesError = UserPreferencesError.corruptedData
        let unknownError = NSError(domain: "test", code: 1, userInfo: nil)
        
        // When
        let convertedNetworkError = AppError.from(networkError)
        let convertedMapError = AppError.from(mapError)
        let convertedCollectionError = AppError.from(collectionError)
        let convertedPreferencesError = AppError.from(preferencesError)
        let convertedUnknownError = AppError.from(unknownError)
        
        // Then
        XCTAssertEqual(convertedNetworkError, .networkUnavailable)
        XCTAssertEqual(convertedMapError, .dataNotFound("Place ID missing"))
        XCTAssertEqual(convertedCollectionError, .dataNotFound("Favorites collection"))
        XCTAssertEqual(convertedPreferencesError, .dataCorrupted("User preferences"))
        
        if case .unknown(let error) = convertedUnknownError {
            XCTAssertEqual((error as NSError).domain, "test")
        } else {
            XCTFail("Should convert unknown error to AppError.unknown")
        }
    }
    
    // MARK: - ErrorHandler Tests
    
    func testErrorHandlerInitialization() {
        XCTAssertNil(errorHandler.currentError, "Should start with no current error")
        XCTAssertFalse(errorHandler.isShowingError, "Should start not showing error")
        XCTAssertTrue(errorHandler.errorHistory.isEmpty, "Should start with empty history")
    }
    
    func testHandleErrorBasic() {
        // Given
        let error = AppError.networkUnavailable
        
        // When
        errorHandler.handle(error)
        
        // Then
        XCTAssertEqual(errorHandler.currentError, error, "Should set current error")
        XCTAssertTrue(errorHandler.isShowingError, "Should show error")
        XCTAssertEqual(errorHandler.errorHistory.count, 1, "Should add to history")
        XCTAssertEqual(errorHandler.errorHistory.first?.error, error, "Should add correct error to history")
    }
    
    func testHandleErrorSilent() {
        // Given
        let error = AppError.operationCancelled
        
        // When
        errorHandler.handle(error, shouldPresentToUser: true)
        
        // Then
        XCTAssertNil(errorHandler.currentError, "Should not set current error for cancelled operation")
        XCTAssertFalse(errorHandler.isShowingError, "Should not show cancelled error")
        XCTAssertEqual(errorHandler.errorHistory.count, 1, "Should still add to history")
    }
    
    func testDismissCurrentError() {
        // Given
        errorHandler.handle(AppError.networkUnavailable)
        
        // When
        errorHandler.dismissCurrentError()
        
        // Then
        XCTAssertNil(errorHandler.currentError, "Should clear current error")
        XCTAssertFalse(errorHandler.isShowingError, "Should stop showing error")
    }
    
    func testClearHistory() {
        // Given
        errorHandler.handle(AppError.networkUnavailable)
        errorHandler.handle(AppError.dataNotFound("test"))
        
        // When
        errorHandler.clearHistory()
        
        // Then
        XCTAssertTrue(errorHandler.errorHistory.isEmpty, "Should clear error history")
    }
    
    func testErrorHistoryLimit() {
        // Given
        let maxHistoryCount = 50
        
        // When - Add more than max history count
        for i in 0..<(maxHistoryCount + 10) {
            errorHandler.handle(AppError.dataNotFound("test\(i)"))
        }
        
        // Then
        XCTAssertEqual(errorHandler.errorHistory.count, maxHistoryCount, "Should limit history size")
    }
    
    func testGetErrorAnalytics() {
        // Given
        let error1 = AppError.networkUnavailable
        let error2 = AppError.dataNotFound("test")
        
        // When
        errorHandler.handle(error1)
        errorHandler.handle(error1)
        errorHandler.handle(error2)
        
        // Then
        let analytics = errorHandler.getErrorAnalytics()
        XCTAssertEqual(analytics[error1.id], 2, "Should count network errors")
        XCTAssertEqual(analytics[error2.id], 1, "Should count data errors")
    }
    
    // MARK: - ErrorSeverity Tests
    
    func testErrorSeverityColors() {
        XCTAssertNotNil(ErrorSeverity.low.color, "Low severity should have color")
        XCTAssertNotNil(ErrorSeverity.medium.color, "Medium severity should have color")
        XCTAssertNotNil(ErrorSeverity.high.color, "High severity should have color")
    }
    
    func testErrorSeveritySystemImages() {
        XCTAssertFalse(ErrorSeverity.low.systemImageName.isEmpty, "Low severity should have system image")
        XCTAssertFalse(ErrorSeverity.medium.systemImageName.isEmpty, "Medium severity should have system image")
        XCTAssertFalse(ErrorSeverity.high.systemImageName.isEmpty, "High severity should have system image")
    }
    
    // MARK: - ErrorHandlerConfiguration Tests
    
    func testErrorHandlerConfigurationDefault() {
        let config = ErrorHandlerConfiguration.default
        
        XCTAssertTrue(config.enableConsoleLogging, "Should enable console logging by default")
        XCTAssertFalse(config.enableFileLogging, "Should disable file logging by default")
        XCTAssertFalse(config.enableCrashReporting, "Should disable crash reporting by default")
        XCTAssertTrue(config.enableAnalytics, "Should enable analytics by default")
        XCTAssertFalse(config.autoRetryNetworkErrors, "Should disable auto retry by default")
        XCTAssertEqual(config.maxRetryAttempts, 3, "Should have correct default retry attempts")
    }
    
    func testCustomErrorHandlerConfiguration() {
        // Given
        let config = ErrorHandlerConfiguration(
            enableConsoleLogging: false,
            enableFileLogging: true,
            enableCrashReporting: true,
            enableAnalytics: false,
            autoRetryNetworkErrors: true,
            maxRetryAttempts: 5
        )
        
        // When
        let customErrorHandler = ErrorHandler(configuration: config)
        
        // Then
        XCTAssertNotNil(customErrorHandler, "Should create error handler with custom config")
    }
    
    // MARK: - Error Presentation Tests
    
    func testErrorPresentationMethods() {
        // Given
        let error = AppError.networkUnavailable
        
        // When & Then - Should not crash
        errorHandler.handle(error, presentationMethod: .alert)
        errorHandler.handle(error, presentationMethod: .toast)
        errorHandler.handle(error, presentationMethod: .banner)
        errorHandler.handle(error, presentationMethod: .silent)
        errorHandler.handle(error, presentationMethod: .custom { _ in })
        
        XCTAssertTrue(true, "All presentation methods should work without crashing")
    }
    
    // MARK: - Performance Tests
    
    func testErrorHandlingPerformance() {
        measure {
            for i in 0..<1000 {
                let error = AppError.dataNotFound("test\(i)")
                errorHandler.handle(error, shouldPresentToUser: false)
            }
        }
    }
    
    func testErrorConversionPerformance() {
        let errors: [Error] = [
            NetworkServiceError.noConnection,
            MapServiceError.missingPlaceID,
            CollectionRepositoryError.favoritesCollectionNotFound,
            UserPreferencesError.corruptedData
        ]
        
        measure {
            for _ in 0..<1000 {
                for error in errors {
                    _ = AppError.from(error)
                }
            }
        }
    }
}