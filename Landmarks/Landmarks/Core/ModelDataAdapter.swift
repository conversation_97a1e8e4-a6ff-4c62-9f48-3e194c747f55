/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Adapter to maintain compatibility between the old ModelData interface and new AppModelData.
*/

import Foundation
import SwiftUI
import MapKit
import Combine

/// Adapter class to maintain compatibility with existing views while using the new architecture
@Observable @MainActor
final class ModelData {
    
    // MARK: - AppModelData Integration
    
    private let appModelData: AppModelData
    
    // MARK: - Compatibility Properties
    
    var landmarks: [Landmark] {
        appModelData.landmarks
    }
    
    var landmarksByContinent: [Continent: [Landmark]] {
        appModelData.landmarksByContinent
    }
    
    var featuredLandmark: Landmark? {
        appModelData.featuredLandmark
    }
    
    var selectedLandmark: Landmark? {
        get { appModelData.selectedLandmark }
        set { appModelData.selectedLandmark = newValue }
    }
    
    var isLandmarkInspectorPresented: Bool {
        get { appModelData.isLandmarkInspectorPresented }
        set { appModelData.isLandmarkInspectorPresented = newValue }
    }
    
    var favoritesCollection: LandmarkCollection? {
        appModelData.favoritesCollection
    }
    
    var userCollections: [LandmarkCollection] {
        appModelData.userCollections
    }
    
    var landmarksById: [Int: Landmark] {
        appModelData.landmarksById
    }
    
    var mapItemsByLandmarkId: [Int: MKMapItem] {
        appModelData.mapItemsByLandmarkId
    }
    
    var mapItemsForLandmarks: [MKMapItem] {
        appModelData.mapItemsForLandmarks
    }
    
    var searchString: String {
        get { appModelData.searchString }
        set { appModelData.searchString = newValue }
    }
    
    var path: NavigationPath {
        get { appModelData.path }
        set { appModelData.path = newValue }
    }
    
    var earnedBadges: [Badge] {
        appModelData.earnedBadges
    }
    
    var windowSize: CGSize {
        get { appModelData.windowSize }
        set { appModelData.windowSize = newValue }
    }
    
    // MARK: - Initialization
    
    init(appModelData: AppModelData = AppModelData()) {
        self.appModelData = appModelData
    }
    
    // MARK: - Landmark Operations
    
    func landmarks(in continent: Continent) -> [Landmark] {
        let landmarks = landmarksByContinent[continent] ?? []
        return landmarks.sorted { String(localized: $0.name) < String(localized: $1.name) }
    }
    
    // MARK: - Collection Operations
    
    func isFavorite(_ landmark: Landmark) -> Bool {
        guard let favoritesCollection = favoritesCollection else { return false }
        return favoritesCollection.landmarks.contains(landmark)
    }
    
    func toggleFavorite(_ landmark: Landmark) {
        Task {
            await appModelData.toggleFavorite(landmark)
        }
    }
    
    func addFavorite(_ landmark: Landmark) {
        guard let favoritesCollection = favoritesCollection else { return }
        Task {
            await appModelData.add(landmark, to: favoritesCollection)
        }
    }
    
    func removeFavorite(_ landmark: Landmark) {
        guard let favoritesCollection = favoritesCollection else { return }
        Task {
            await appModelData.remove(landmark, from: favoritesCollection)
        }
    }
    
    func addUserCollection() -> LandmarkCollection {
        // For compatibility, return a temporary collection
        // The actual creation happens asynchronously
        let tempCollection = LandmarkCollection(
            id: 0,
            name: String(localized: "New Collection"),
            description: String(localized: "Add a description for your collection here…"),
            landmarkIds: [],
            landmarks: []
        )
        
        Task {
            _ = await appModelData.addUserCollection()
        }
        
        return tempCollection
    }
    
    func remove(_ collection: LandmarkCollection) {
        Task {
            await appModelData.remove(collection)
        }
    }
    
    func collection(_ collection: LandmarkCollection, contains landmark: Landmark) -> Bool {
        return appModelData.collection(collection, contains: landmark)
    }
    
    func collectionsContaining(_ landmark: Landmark) -> [LandmarkCollection] {
        // For compatibility, return cached result
        // The actual query happens asynchronously
        return userCollections.filter { collection in
            collection.landmarks.contains(landmark)
        }
    }
    
    func add(_ landmark: Landmark, to collection: LandmarkCollection) {
        Task {
            await appModelData.add(landmark, to: collection)
        }
    }
    
    func remove(_ landmark: Landmark, from collection: LandmarkCollection) {
        Task {
            await appModelData.remove(landmark, from: collection)
        }
    }
    
    // MARK: - Type Aliases for Compatibility
    
    typealias Continent = ModelData.Continent
    
    // MARK: - Static Properties for Compatibility
    
    static let orderedContinents: [Continent] = [.asia, .africa, .antarctica, .australiaOceania, .europe, .northAmerica, .southAmerica]
}

// MARK: - Continent Extension

extension ModelData {
    enum Continent: String, CaseIterable {
        case africa = "Africa"
        case antarctica = "Antarctica"
        case asia = "Asia"
        case australiaOceania = "Australia/Oceania"
        case europe = "Europe"
        case northAmerica = "North America"
        case southAmerica = "South America"
        
        var name: String {
            switch self {
            case .africa: String(localized: "Africa", comment: "The name of a continent.")
            case .antarctica: String(localized: "Antarctica", comment: "The name of a continent.")
            case .asia: String(localized: "Asia", comment: "The name of a continent.")
            case .australiaOceania: String(localized: "Australia/Oceania", comment: "The name of a continent.")
            case .europe: String(localized: "Europe", comment: "The name of a continent.")
            case .northAmerica: String(localized: "North America", comment: "The name of a continent.")
            case .southAmerica: String(localized: "South America", comment: "The name of a continent.")
            }
        }
    }
}

// MARK: - Environment Integration

extension EnvironmentValues {
    @Entry var appModelData: AppModelData = AppModelData()
}

extension View {
    /// Provides backward compatibility for views expecting ModelData
    func withModelDataCompatibility() -> some View {
        modifier(ModelDataCompatibilityModifier())
    }
}

/// View modifier that provides ModelData compatibility
private struct ModelDataCompatibilityModifier: ViewModifier {
    @Environment(\.appModelData) var appModelData
    
    func body(content: Content) -> some View {
        content
            .environment(ModelData(appModelData: appModelData))
    }
}