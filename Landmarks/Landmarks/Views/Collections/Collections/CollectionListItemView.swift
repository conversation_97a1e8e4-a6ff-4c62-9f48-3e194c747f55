/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
A view that represents a single landmark collection in a grid.
*/

import SwiftUI

/// A view that represents a single landmark collection in a grid.
struct CollectionListItemView: View {
    let collection: LandmarkCollection

    var body: some View {
        VStack(spacing: Constants.standardPadding / 2) {
            collection.imageForListItem()
                .cornerRadius(Constants.cornerRadius)

            VStack(spacing: Constants.compactLineSpacing) {
                Text(collection.name)
                    .responsiveTypography(.subtitle)
                    .processedText(.subtitle)
                    .smartTextAlignment(for: collection.name)

                Text("\(collection.landmarks.count) items")
                    .responsiveTypography(.caption, color: .secondary)
                    .processedText(.caption)
            }
        }
    }
}

#Preview {
    let modelData = ModelData()
    let previewCollection = modelData.userCollections.first!

    CollectionListItemView(collection: previewCollection)
}
