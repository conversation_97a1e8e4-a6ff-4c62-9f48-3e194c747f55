/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Protocol definitions for various services used throughout the app.
*/

import Foundation
import MapKit
import Combine

// MARK: - Network Service Protocol

/// Protocol for network operations
protocol NetworkServiceProtocol {
    /// Performs a generic network request
    func request<T: Decodable>(_ endpoint: NetworkEndpoint, responseType: T.Type) async throws -> T
    
    /// Downloads data from a URL
    func downloadData(from url: URL) async throws -> Data
    
    /// Uploads data to a URL
    func uploadData(_ data: Data, to url: URL) async throws -> Data
    
    /// Cancels all pending requests
    func cancelAllRequests()
}

// MARK: - Map Service Protocol

/// Protocol for map-related operations
protocol MapServiceProtocol {
    /// Fetches map items for landmarks
    func fetchMapItems(for landmarks: [Landmark]) async throws -> [Int: MKMapItem]
    
    /// Fetches a single map item for a landmark
    func fetchMapItem(for landmark: Landmark) async throws -> MKMapItem?
    
    /// Searches for places near a coordinate
    func searchPlaces(near coordinate: CLLocationCoordinate2D, radius: CLLocationDistance) async throws -> [MKMapItem]
    
    /// Gets directions between two map items
    func getDirections(from source: MKMapItem, to destination: MKMapItem) async throws -> MKDirections.Response
}

// MARK: - User Preferences Service Protocol

/// Protocol for managing user preferences and settings
protocol UserPreferencesServiceProtocol {
    /// Gets a preference value
    func getValue<T>(for key: PreferenceKey<T>) -> T
    
    /// Sets a preference value
    func setValue<T>(_ value: T, for key: PreferenceKey<T>)
    
    /// Removes a preference
    func removeValue(for key: String)
    
    /// Observes changes to a specific preference
    func observeChanges<T>(for key: PreferenceKey<T>) -> AnyPublisher<T, Never>
    
    /// Resets all preferences to defaults
    func resetToDefaults()
}

// MARK: - Location Service Protocol

/// Protocol for location-related operations
protocol LocationServiceProtocol {
    /// Current location authorization status
    var authorizationStatus: CLAuthorizationStatus { get }
    
    /// Requests location authorization
    func requestLocationAuthorization() async throws -> CLAuthorizationStatus
    
    /// Gets the current location
    func getCurrentLocation() async throws -> CLLocation
    
    /// Observes location updates
    func observeLocationUpdates() -> AnyPublisher<CLLocation, Error>
    
    /// Stops location updates
    func stopLocationUpdates()
}

// MARK: - Analytics Service Protocol

/// Protocol for analytics and tracking
protocol AnalyticsServiceProtocol {
    /// Tracks an event
    func trackEvent(_ event: AnalyticsEvent)
    
    /// Tracks a screen view
    func trackScreenView(_ screenName: String, parameters: [String: Any]?)
    
    /// Sets user properties
    func setUserProperties(_ properties: [String: Any])
    
    /// Enables or disables analytics
    func setAnalyticsEnabled(_ enabled: Bool)
}

// MARK: - Supporting Types

/// Network endpoint definition
struct NetworkEndpoint {
    let url: URL
    let method: HTTPMethod
    let headers: [String: String]?
    let body: Data?
    
    enum HTTPMethod: String {
        case GET = "GET"
        case POST = "POST"
        case PUT = "PUT"
        case DELETE = "DELETE"
        case PATCH = "PATCH"
    }
}

/// Preference key for type-safe user preferences
struct PreferenceKey<T> {
    let key: String
    let defaultValue: T
    
    init(_ key: String, defaultValue: T) {
        self.key = key
        self.defaultValue = defaultValue
    }
}

/// Analytics event
struct AnalyticsEvent {
    let name: String
    let parameters: [String: Any]?
    let timestamp: Date
    
    init(name: String, parameters: [String: Any]? = nil) {
        self.name = name
        self.parameters = parameters
        self.timestamp = Date()
    }
}

// MARK: - Predefined Preference Keys

extension PreferenceKey {
    static let isFirstLaunch = PreferenceKey("isFirstLaunch", defaultValue: true)
    static let enableLocationServices = PreferenceKey("enableLocationServices", defaultValue: false)
    static let enableAnalytics = PreferenceKey("enableAnalytics", defaultValue: true)
    static let preferredMapType = PreferenceKey("preferredMapType", defaultValue: MKMapType.standard.rawValue)
    static let enableNotifications = PreferenceKey("enableNotifications", defaultValue: true)
    static let selectedTheme = PreferenceKey("selectedTheme", defaultValue: "system")
    static let cacheSizeLimit = PreferenceKey("cacheSizeLimit", defaultValue: 100) // MB
}