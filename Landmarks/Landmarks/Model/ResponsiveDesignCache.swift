/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Enhanced responsive design system with caching for improved performance.
*/

import SwiftUI
import Combine
#if os(iOS)
import UIKit
#elseif os(macOS)
import AppKit
#endif

/// Enhanced responsive design system with caching capabilities
@MainActor
final class ResponsiveDesignCache: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = ResponsiveDesignCache()
    
    // MARK: - Published Properties
    
    @Published var currentDeviceType: ResponsiveDesign.DeviceType
    @Published var currentScreenSize: ResponsiveDesign.ScreenSize
    @Published var currentTypographyScale: ResponsiveDesign.TypographyScale
    @Published var currentSpacingScale: ResponsiveDesign.SpacingScale
    @Published var currentLayoutAdaptation: ResponsiveDesign.LayoutAdaptation
    
    // MARK: - Cache
    
    private var deviceTypeCache: ResponsiveDesign.DeviceType?
    private var screenSizeCache: ResponsiveDesign.ScreenSize?
    private var typographyScaleCache: [ResponsiveDesign.DeviceType: ResponsiveDesign.TypographyScale] = [:]
    private var spacingScaleCache: [ResponsiveDesign.DeviceType: ResponsiveDesign.SpacingScale] = [:]
    private var layoutAdaptationCache: [ResponsiveDesign.ScreenSize: ResponsiveDesign.LayoutAdaptation] = [:]
    
    // MARK: - Observation
    
    private var cancellables = Set<AnyCancellable>()
    private var orientationObserver: NSObjectProtocol?
    
    // MARK: - Initialization
    
    nonisolated private init() {
        // Initialize with current values
        self.currentDeviceType = ResponsiveDesign.DeviceType.current
        self.currentScreenSize = ResponsiveDesign.ScreenSize.current
        self.currentTypographyScale = ResponsiveDesign.TypographyScale.current
        self.currentSpacingScale = ResponsiveDesign.SpacingScale.current
        self.currentLayoutAdaptation = ResponsiveDesign.LayoutAdaptation.current
        
        // Cache initial values
        cacheCurrentValues()
        
        // Setup orientation observation
        setupOrientationObservation()
    }
    
    deinit {
        if let observer = orientationObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
    
    // MARK: - Public Methods
    
    /// Gets cached device type or computes and caches if needed
    func getDeviceType() -> ResponsiveDesign.DeviceType {
        if let cached = deviceTypeCache {
            return cached
        }
        
        let deviceType = ResponsiveDesign.DeviceType.current
        deviceTypeCache = deviceType
        return deviceType
    }
    
    /// Gets cached screen size or computes and caches if needed
    func getScreenSize() -> ResponsiveDesign.ScreenSize {
        if let cached = screenSizeCache {
            return cached
        }
        
        let screenSize = ResponsiveDesign.ScreenSize.current
        screenSizeCache = screenSize
        return screenSize
    }
    
    /// Gets cached typography scale for the current device
    func getTypographyScale() -> ResponsiveDesign.TypographyScale {
        let deviceType = getDeviceType()
        
        if let cached = typographyScaleCache[deviceType] {
            return cached
        }
        
        let scale = ResponsiveDesign.TypographyScale.current
        typographyScaleCache[deviceType] = scale
        return scale
    }
    
    /// Gets cached spacing scale for the current device
    func getSpacingScale() -> ResponsiveDesign.SpacingScale {
        let deviceType = getDeviceType()
        
        if let cached = spacingScaleCache[deviceType] {
            return cached
        }
        
        let scale = ResponsiveDesign.SpacingScale.current
        spacingScaleCache[deviceType] = scale
        return scale
    }
    
    /// Gets cached layout adaptation for the current screen size
    func getLayoutAdaptation() -> ResponsiveDesign.LayoutAdaptation {
        let screenSize = getScreenSize()
        
        if let cached = layoutAdaptationCache[screenSize] {
            return cached
        }
        
        let adaptation = ResponsiveDesign.LayoutAdaptation.current
        layoutAdaptationCache[screenSize] = adaptation
        return adaptation
    }
    
    /// Gets scale factor for a typography style
    func getScaleFactor(for style: Typography.TextStyle) -> CGFloat {
        return getTypographyScale().scale(for: style)
    }
    
    /// Gets spacing value for a spacing level
    func getSpacing(_ level: SpacingLevel) -> CGFloat {
        let scale = getSpacingScale()
        switch level {
        case .small:
            return scale.small
        case .medium:
            return scale.medium
        case .large:
            return scale.large
        case .extraLarge:
            return scale.extraLarge
        }
    }
    
    /// Forces cache refresh (useful after orientation changes)
    func refreshCache() {
        clearCache()
        updateCurrentValues()
        cacheCurrentValues()
    }
    
    /// Clears all cached values
    func clearCache() {
        deviceTypeCache = nil
        screenSizeCache = nil
        typographyScaleCache.removeAll()
        spacingScaleCache.removeAll()
        layoutAdaptationCache.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func cacheCurrentValues() {
        deviceTypeCache = currentDeviceType
        screenSizeCache = currentScreenSize
        typographyScaleCache[currentDeviceType] = currentTypographyScale
        spacingScaleCache[currentDeviceType] = currentSpacingScale
        layoutAdaptationCache[currentScreenSize] = currentLayoutAdaptation
    }
    
    private func updateCurrentValues() {
        currentDeviceType = ResponsiveDesign.DeviceType.current
        currentScreenSize = ResponsiveDesign.ScreenSize.current
        currentTypographyScale = ResponsiveDesign.TypographyScale.current
        currentSpacingScale = ResponsiveDesign.SpacingScale.current
        currentLayoutAdaptation = ResponsiveDesign.LayoutAdaptation.current
    }
    
    private func setupOrientationObservation() {
        #if os(iOS)
        orientationObserver = NotificationCenter.default.addObserver(
            forName: UIDevice.orientationDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleOrientationChange()
        }
        #elseif os(macOS)
        orientationObserver = NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleOrientationChange()
        }
        #endif
    }
    
    private func handleOrientationChange() {
        Task { @MainActor in
            // Small delay to ensure the orientation change is complete
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            refreshCache()
        }
    }
}

// MARK: - Supporting Types

/// Spacing levels for easier access
enum SpacingLevel {
    case small
    case medium
    case large
    case extraLarge
}

// MARK: - Cached View Extensions

extension Text {
    /// Applies cached responsive typography scaling
    @MainActor
    func cachedResponsiveTypography(_ style: Typography.TextStyle, color: Typography.TextColor = .primary) -> some View {
        let scale = ResponsiveDesignCache.shared.getScaleFactor(for: style)
        return self
            .typography(style, color: color)
            .scaleEffect(scale)
    }
    
    /// Applies cached responsive typography with image readability enhancements
    @MainActor
    func cachedResponsiveTypographyOnImage(_ style: Typography.TextStyle, color: Typography.TextColor = .onImage) -> some View {
        let scale = ResponsiveDesignCache.shared.getScaleFactor(for: style)
        return self
            .typographyOnImageEnhanced(style, color: color)
            .scaleEffect(scale)
    }
}

extension View {
    /// Applies cached responsive content insets
    @MainActor
    func cachedResponsiveContentInsets() -> some View {
        let inset = ResponsiveDesignCache.shared.getLayoutAdaptation().contentInset
        return self.padding(.horizontal, inset)
    }
    
    /// Applies cached responsive spacing
    @MainActor
    func cachedResponsiveSpacing(_ level: SpacingLevel) -> some View {
        let spacing = ResponsiveDesignCache.shared.getSpacing(level)
        return self.padding(spacing)
    }
    
    /// Applies cached responsive corner radius
    @MainActor
    func cachedResponsiveCornerRadius() -> some View {
        let radius = ResponsiveDesignCache.shared.getLayoutAdaptation().cardCornerRadius
        return self.cornerRadius(radius)
    }
    
    /// Applies responsive grid configuration
    @MainActor
    func responsiveGrid<Item: Identifiable, Content: View>(
        _ items: [Item],
        @ViewBuilder content: @escaping (Item) -> Content
    ) -> some View {
        let adaptation = ResponsiveDesignCache.shared.getLayoutAdaptation()
        let columns = Array(repeating: GridItem(.flexible(), spacing: adaptation.gridSpacing), 
                           count: adaptation.gridColumns)
        
        return LazyVGrid(columns: columns, spacing: adaptation.gridSpacing) {
            ForEach(items) { item in
                content(item)
            }
        }
    }
}

// MARK: - Performance Monitoring

extension ResponsiveDesignCache {
    /// Gets cache performance statistics
    func getCacheStats() -> CacheStats {
        return CacheStats(
            deviceTypeCacheHit: deviceTypeCache != nil,
            screenSizeCacheHit: screenSizeCache != nil,
            typographyScaleCacheSize: typographyScaleCache.count,
            spacingScaleCacheSize: spacingScaleCache.count,
            layoutAdaptationCacheSize: layoutAdaptationCache.count
        )
    }
}

/// Cache performance statistics
struct CacheStats {
    let deviceTypeCacheHit: Bool
    let screenSizeCacheHit: Bool
    let typographyScaleCacheSize: Int
    let spacingScaleCacheSize: Int
    let layoutAdaptationCacheSize: Int
    
    var totalCacheSize: Int {
        return typographyScaleCacheSize + spacingScaleCacheSize + layoutAdaptationCacheSize
    }
    
    var cacheEfficiency: Double {
        let hits = (deviceTypeCacheHit ? 1 : 0) + (screenSizeCacheHit ? 1 : 0)
        return Double(hits) / 2.0
    }
}