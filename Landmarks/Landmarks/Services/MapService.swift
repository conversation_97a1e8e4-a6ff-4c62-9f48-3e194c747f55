/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Service for handling MapKit operations with optimized batch loading and caching.
*/

import Foundation
import MapKit
import Combine
import CoreLocation

/// Service for handling MapKit operations
final class MapService: MapServiceProtocol {
    
    // MARK: - Properties
    
    private var mapItemCache: [String: CachedMapItem] = [:]
    private let cacheQueue = DispatchQueue(label: "mapservice.cache", attributes: .concurrent)
    private let requestQueue = DispatchQueue(label: "mapservice.requests", attributes: .concurrent)
    private let maxConcurrentRequests = 5
    private let cacheTimeToLive: TimeInterval = 3600 // 1 hour
    
    // MARK: - MapServiceProtocol
    
    func fetchMapItems(for landmarks: [Landmark]) async throws -> [Int: MKMapItem] {
        var result: [Int: MKMapItem] = [:]
        
        // Filter landmarks that have placeIDs
        let landmarksWithPlaceIDs = landmarks.compactMap { landmark -> (Landmark, String)? in
            guard let placeID = landmark.placeID else { return nil }
            return (landmark, placeID)
        }
        
        // Process in batches to avoid overwhelming the system
        let batches = landmarksWithPlaceIDs.chunked(into: maxConcurrentRequests)
        
        for batch in batches {
            let batchResults = await withTaskGroup(of: (Int, MKMapItem?).self) { group in
                var results: [(Int, MKMapItem?)] = []
                
                for (landmark, placeID) in batch {
                    group.addTask {
                        do {
                            let mapItem = try await self.fetchMapItem(placeID: placeID)
                            return (landmark.id, mapItem)
                        } catch {
                            print("Failed to fetch map item for landmark \(landmark.id): \(error)")
                            return (landmark.id, nil)
                        }
                    }
                }
                
                for await result in group {
                    results.append(result)
                }
                
                return results
            }
            
            // Add successful results to the final dictionary
            for (landmarkId, mapItem) in batchResults {
                if let mapItem = mapItem {
                    result[landmarkId] = mapItem
                }
            }
        }
        
        return result
    }
    
    func fetchMapItem(for landmark: Landmark) async throws -> MKMapItem? {
        guard let placeID = landmark.placeID else {
            throw MapServiceError.missingPlaceID
        }
        
        return try await fetchMapItem(placeID: placeID)
    }
    
    func searchPlaces(near coordinate: CLLocationCoordinate2D, radius: CLLocationDistance) async throws -> [MKMapItem] {
        let request = MKLocalSearch.Request()
        request.naturalLanguageQuery = "landmark"
        request.region = MKCoordinateRegion(
            center: coordinate,
            latitudinalMeters: radius,
            longitudinalMeters: radius
        )
        
        let search = MKLocalSearch(request: request)
        let response = try await search.start()
        
        return response.mapItems
    }
    
    func getDirections(from source: MKMapItem, to destination: MKMapItem) async throws -> MKDirections.Response {
        let request = MKDirections.Request()
        request.source = source
        request.destination = destination
        request.transportType = .automobile
        
        let directions = MKDirections(request: request)
        return try await directions.calculate()
    }
    
    // MARK: - Private Methods
    
    private func fetchMapItem(placeID: String) async throws -> MKMapItem? {
        // Check cache first
        if let cachedItem = getCachedMapItem(for: placeID) {
            return cachedItem
        }
        
        // Create identifier and request
        guard let identifier = MKMapItem.Identifier(rawValue: placeID) else {
            throw MapServiceError.invalidPlaceID(placeID)
        }
        
        let request = MKMapItemRequest(mapItemIdentifier: identifier)
        
        do {
            let mapItem = try await request.mapItem
            cacheMapItem(mapItem, for: placeID)
            return mapItem
        } catch {
            throw MapServiceError.requestFailed(error)
        }
    }
    
    private func getCachedMapItem(for placeID: String) -> MKMapItem? {
        return cacheQueue.sync {
            guard let cachedItem = mapItemCache[placeID] else { return nil }
            
            // Check if cache entry is still valid
            if cachedItem.isExpired {
                mapItemCache.removeValue(forKey: placeID)
                return nil
            }
            
            return cachedItem.mapItem
        }
    }
    
    private func cacheMapItem(_ mapItem: MKMapItem, for placeID: String) {
        cacheQueue.async(flags: .barrier) {
            self.mapItemCache[placeID] = CachedMapItem(
                mapItem: mapItem,
                timestamp: Date(),
                ttl: self.cacheTimeToLive
            )
        }
    }
    
    /// Cleans up expired cache entries
    func cleanupCache() {
        cacheQueue.async(flags: .barrier) {
            let now = Date()
            self.mapItemCache = self.mapItemCache.filter { !$0.value.isExpired(at: now) }
        }
    }
}

// MARK: - Supporting Types

private struct CachedMapItem {
    let mapItem: MKMapItem
    let timestamp: Date
    let ttl: TimeInterval
    
    var isExpired: Bool {
        return isExpired(at: Date())
    }
    
    func isExpired(at date: Date) -> Bool {
        return date.timeIntervalSince(timestamp) > ttl
    }
}

// MARK: - Map Service Errors

enum MapServiceError: Error, LocalizedError {
    case missingPlaceID
    case invalidPlaceID(String)
    case requestFailed(Error)
    case networkUnavailable
    
    var errorDescription: String? {
        switch self {
        case .missingPlaceID:
            return "Landmark is missing a place ID"
        case .invalidPlaceID(let placeID):
            return "Invalid place ID: \(placeID)"
        case .requestFailed(let error):
            return "Map request failed: \(error.localizedDescription)"
        case .networkUnavailable:
            return "Network is unavailable"
        }
    }
}

// MARK: - Array Extension for Batching

private extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}