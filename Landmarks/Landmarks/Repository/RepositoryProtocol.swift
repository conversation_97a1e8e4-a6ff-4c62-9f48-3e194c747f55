/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Protocol definitions for the Repository pattern, providing abstract data access layer.
*/

import Foundation
import Combine

/// Base protocol for all repositories, defining common operations
protocol RepositoryProtocol {
    associatedtype Entity: Identifiable
    associatedtype ID: Hashable where ID == Entity.ID
    
    /// Fetches all entities
    func fetchAll() async throws -> [Entity]
    
    /// Fetches a specific entity by ID
    func fetch(by id: ID) async throws -> Entity?
    
    /// Saves or updates an entity
    func save(_ entity: Entity) async throws -> Entity
    
    /// Deletes an entity by ID
    func delete(by id: ID) async throws
    
    /// Observes changes to entities
    func observe() -> AnyPublisher<[Entity], Never>
}

/// Protocol for repositories that support searching
protocol SearchableRepository: RepositoryProtocol {
    /// Searches entities based on a query string
    func search(query: String) async throws -> [Entity]
    
    /// Filters entities based on predicate
    func filter(_ predicate: @escaping (Entity) -> Bool) async throws -> [Entity]
}

/// Protocol for repositories that support pagination
protocol PaginatableRepository: RepositoryProtocol {
    /// Fetches entities with pagination
    func fetch(offset: Int, limit: Int) async throws -> [Entity]
    
    /// Gets total count of entities
    func count() async throws -> Int
}

/// Protocol for repositories that support batch operations
protocol BatchOperationRepository: RepositoryProtocol {
    /// Saves multiple entities in a batch
    func saveBatch(_ entities: [Entity]) async throws -> [Entity]
    
    /// Deletes multiple entities by IDs
    func deleteBatch(ids: [ID]) async throws
}

/// Repository configuration for data source management
struct RepositoryConfiguration {
    let cacheEnabled: Bool
    let cacheTTL: TimeInterval
    let enableOfflineMode: Bool
    let syncWithRemote: Bool
    
    static let `default` = RepositoryConfiguration(
        cacheEnabled: true,
        cacheTTL: 300, // 5 minutes
        enableOfflineMode: true,
        syncWithRemote: false
    )
}

/// Repository state for tracking data operations
enum RepositoryState {
    case idle
    case loading
    case loaded
    case error(Error)
    case syncing
}