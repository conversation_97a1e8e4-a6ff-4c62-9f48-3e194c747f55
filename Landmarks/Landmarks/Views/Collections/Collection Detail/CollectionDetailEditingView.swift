/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
A view that lets a person edit a collection's title, description, and list of landmarks.
*/

import SwiftUI

/// A view that lets a person edit a collection's title, description, and list of landmarks.
struct CollectionDetailEditingView: View {
    
    @Bindable var collection: LandmarkCollection
    @Binding var isShowingLandmarksSelection: Bool
    @Binding var isShowingDeleteConfirmation: Bool
    
    var body: some View {
        VStack {
            HStack {
                Text("Title")
                    .responsiveTypography(.title, color: .secondary)
                Spacer()
            }
            .padding(.bottom, collection.isFavoritesCollection ? 0 : Constants.compactLineSpacing)
            .padding(.top, Constants.standardPadding)
            
            VStack {
                if collection.isFavoritesCollection {
                    HStack {
                        Text(collection.name)
                            .responsiveTypography(.heroTitle)
                            .lineLimit(2)
                            .minimumScaleFactor(0.8)
                        Spacer()
                    }
                    .padding()
                } else {
                    TextField("Name", text: $collection.name)
                        .responsiveTypography(.title)
                        .padding()
                        .textFieldStyle(.plain)
                }
            }
            .background(Constants.editingBackgroundStyle, in: RoundedRectangle(cornerRadius: Constants.cornerRadius))
            
            HStack {
                Text("Description")
                    .typography(.title, color: .secondary)
                Spacer()
            }
            .padding(.bottom, Constants.compactLineSpacing)
            .padding(.top, Constants.standardPadding)
            
            if !collection.isFavoritesCollection {
                VStack {
                    EnhancedTextEditor(
                        text: $collection.description,
                        placeholder: "Describe your collection...",
                        minHeight: Constants.textEditorHeight,
                        maxHeight: 200,
                        contentType: .description
                    )
                    .padding()
                }
                .background(Constants.editingBackgroundStyle, in: RoundedRectangle(cornerRadius: Constants.cornerRadius))
            }
            
            HStack {
                Text("Landmarks")
                    .typography(.title, color: .secondary)
                Spacer()
            }
            .padding(.top, Constants.standardPadding)
            
            VStack {
                HStack {
                    Spacer()
                    Button("Select") {
                        isShowingLandmarksSelection.toggle()
                    }
                    .padding([.top, .leading, .trailing])
                }
                LandmarksGrid(landmarks: $collection.landmarks, forEditing: true)
                    .padding([.leading, .trailing, .bottom])
            }
            .background(Constants.editingBackgroundStyle, in: RoundedRectangle(cornerRadius: Constants.cornerRadius))
        }
        .responsiveContentInsets()
        
    }
}

#Preview {
    let modelData = ModelData()
    let previewCollection = modelData.userCollections.last!

    CollectionDetailEditingView(collection: previewCollection,
                                isShowingLandmarksSelection: .constant(false),
                                isShowingDeleteConfirmation: .constant(false))
}

