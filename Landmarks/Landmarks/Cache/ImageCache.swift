/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
High-performance image caching system with memory and disk caching capabilities.
*/

import Foundation
import SwiftUI
import CryptoKit
#if os(iOS)
import UIKit
typealias PlatformImage = UIImage
#elseif os(macOS)
import AppKit
typealias PlatformImage = NSImage
#endif

/// High-performance image caching system
@MainActor
final class ImageCache: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = ImageCache()
    
    // MARK: - Configuration
    
    private let configuration: ImageCacheConfiguration
    
    // MARK: - Memory Cache
    
    private let memoryCache = NSCache<NSString, PlatformImage>()
    
    // MARK: - Disk Cache
    
    private let diskCacheQueue = DispatchQueue(label: "imagecache.disk", qos: .utility)
    private let fileManager = FileManager.default
    private let diskCacheURL: URL
    
    // MARK: - Cache Statistics
    
    @Published var cacheStatistics = CacheStatistics()
    
    // MARK: - Cleanup
    
    private var cleanupTimer: Timer?
    
    // MARK: - Initialization
    
    nonisolated init(configuration: ImageCacheConfiguration = .default) {
        self.configuration = configuration
        
        // Setup memory cache
        memoryCache.countLimit = configuration.memoryCountLimit
        memoryCache.totalCostLimit = configuration.memoryCostLimit
        
        // Setup disk cache directory
        let cacheDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        diskCacheURL = cacheDirectory.appendingPathComponent("ImageCache")
        
        // Create disk cache directory if needed
        try? fileManager.createDirectory(at: diskCacheURL, withIntermediateDirectories: true)
        
        // Setup cleanup timer
        setupCleanupTimer()
        
        // Listen for memory warnings
        #if os(iOS)
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
        #endif
    }
    
    deinit {
        cleanupTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Public Methods
    
    /// Retrieves an image from cache
    func image(for key: String) -> PlatformImage? {
        // Check memory cache first
        if let image = memoryCache.object(forKey: key as NSString) {
            updateStatistics(memoryHit: true)
            return image
        }
        
        // Check disk cache
        if let image = loadImageFromDisk(key: key) {
            // Store in memory cache for faster access
            storeImageInMemory(image, key: key)
            updateStatistics(diskHit: true)
            return image
        }
        
        updateStatistics(miss: true)
        return nil
    }
    
    /// Stores an image in cache
    func store(_ image: PlatformImage, for key: String) {
        let cost = imageCost(image)
        
        // Store in memory cache
        storeImageInMemory(image, key: key, cost: cost)
        
        // Store in disk cache asynchronously
        diskCacheQueue.async {
            self.storeImageOnDisk(image, key: key)
        }
    }
    
    /// Removes an image from cache
    func removeImage(for key: String) {
        // Remove from memory
        memoryCache.removeObject(forKey: key as NSString)
        
        // Remove from disk
        diskCacheQueue.async {
            self.removeImageFromDisk(key: key)
        }
    }
    
    /// Clears all cached images
    func clearCache() {
        // Clear memory cache
        memoryCache.removeAllObjects()
        
        // Clear disk cache
        diskCacheQueue.async {
            self.clearDiskCache()
        }
        
        // Reset statistics
        cacheStatistics = CacheStatistics()
    }
    
    /// Gets cache size information
    func getCacheSize() async -> CacheSizeInfo {
        let memorySize = calculateMemoryCacheSize()
        let diskSize = await calculateDiskCacheSize()
        
        return CacheSizeInfo(
            memorySize: memorySize,
            diskSize: diskSize,
            totalSize: memorySize + diskSize
        )
    }
    
    /// Performs cache cleanup
    func performCleanup() {
        diskCacheQueue.async {
            self.cleanupExpiredImages()
            self.enforceMaxDiskCacheSize()
        }
    }
    
    // MARK: - Private Methods - Memory Cache
    
    private func storeImageInMemory(_ image: PlatformImage, key: String, cost: Int = 0) {
        let actualCost = cost > 0 ? cost : imageCost(image)
        memoryCache.setObject(image, forKey: key as NSString, cost: actualCost)
    }
    
    private func imageCost(_ image: PlatformImage) -> Int {
#if os(iOS)
        return Int(image.size.width * image.size.height * image.scale * image.scale * 4) // 4 bytes per pixel
#elseif os(macOS)
        let scale: CGFloat = 1.0 // NSImage doesn't have a scale property like UIImage
        return Int(image.size.width * image.size.height * scale * scale * 4) // 4 bytes per pixel
#endif
    }
    
    private func calculateMemoryCacheSize() -> Int {
        // NSCache doesn't provide direct size calculation
        // This is an approximation based on cost
        return memoryCache.totalCostLimit > 0 ? min(Int(memoryCache.totalCostLimit * 0.7), configuration.memoryCostLimit) : 0
    }
    
    // MARK: - Private Methods - Disk Cache
    
    private func loadImageFromDisk(key: String) -> PlatformImage? {
        let fileURL = diskFileURL(for: key)
        
        guard fileManager.fileExists(atPath: fileURL.path),
              let data = try? Data(contentsOf: fileURL),
              let image = PlatformImage(data: data) else {
            return nil
        }
        
        // Update access time for LRU
        updateAccessTime(for: fileURL)
        
        return image
    }
    
    private func storeImageOnDisk(_ image: PlatformImage, key: String) {
        let fileURL = diskFileURL(for: key)
        
#if os(iOS)
        guard let data = image.jpegData(compressionQuality: configuration.diskCompressionQuality) else {
#elseif os(macOS)
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil),
              let bitmapRep = NSBitmapImageRep(cgImage: cgImage),
              let data = bitmapRep.representation(using: .jpeg, properties: [.compressionFactor: configuration.diskCompressionQuality]) else {
#endif
            return
        }
        
        do {
            try data.write(to: fileURL)
            setImageMetadata(for: fileURL, size: data.count)
        } catch {
            print("Failed to store image on disk: \(error)")
        }
    }
    
    private func removeImageFromDisk(key: String) {
        let fileURL = diskFileURL(for: key)
        try? fileManager.removeItem(at: fileURL)
    }
    
    private func clearDiskCache() {
        guard let fileURLs = try? fileManager.contentsOfDirectory(at: diskCacheURL, includingPropertiesForKeys: nil) else {
            return
        }
        
        for fileURL in fileURLs {
            try? fileManager.removeItem(at: fileURL)
        }
    }
    
    private func diskFileURL(for key: String) -> URL {
        let hashedKey = key.sha256
        return diskCacheURL.appendingPathComponent(hashedKey).appendingPathExtension("jpg")
    }
    
    private func updateAccessTime(for fileURL: URL) {
        try? fileManager.setAttributes([.modificationDate: Date()], ofItemAtPath: fileURL.path)
    }
    
    private func setImageMetadata(for fileURL: URL, size: Int) {
        let metadata = [
            "size": size,
            "createdAt": Date().timeIntervalSince1970
        ]
        
        let metadataURL = fileURL.appendingPathExtension("meta")
        let data = try? JSONSerialization.data(withJSONObject: metadata)
        try? data?.write(to: metadataURL)
    }
    
    // MARK: - Private Methods - Cleanup
    
    private func setupCleanupTimer() {
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: configuration.cleanupInterval, repeats: true) { _ in
            self.performCleanup()
        }
    }
    
    private func cleanupExpiredImages() {
        guard let fileURLs = try? fileManager.contentsOfDirectory(
            at: diskCacheURL,
            includingPropertiesForKeys: [.contentModificationDateKey],
            options: []
        ) else {
            return
        }
        
        let expirationDate = Date().addingTimeInterval(-configuration.maxAge)
        
        for fileURL in fileURLs {
            guard let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
                  let modificationDate = attributes[.modificationDate] as? Date else {
                continue
            }
            
            if modificationDate < expirationDate {
                try? fileManager.removeItem(at: fileURL)
                // Also remove metadata file
                let metadataURL = fileURL.appendingPathExtension("meta")
                try? fileManager.removeItem(at: metadataURL)
            }
        }
    }
    
    private func enforceMaxDiskCacheSize() {
        let currentSize = calculateDiskCacheSizeSync()
        
        guard currentSize > configuration.maxDiskCacheSize else {
            return
        }
        
        // Get files sorted by modification date (LRU)
        guard let fileURLs = try? fileManager.contentsOfDirectory(
            at: diskCacheURL,
            includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey],
            options: []
        ) else {
            return
        }
        
        let sortedFiles = fileURLs
            .compactMap { url -> (URL, Date, Int)? in
                guard let attributes = try? fileManager.attributesOfItem(atPath: url.path),
                      let modificationDate = attributes[.modificationDate] as? Date,
                      let size = attributes[.fileSize] as? Int else {
                    return nil
                }
                return (url, modificationDate, size)
            }
            .sorted { $0.1 < $1.1 } // Sort by modification date
        
        var currentCacheSize = currentSize
        let targetSize = Int(Double(configuration.maxDiskCacheSize) * 0.8) // Reduce to 80% of max
        
        for (fileURL, _, fileSize) in sortedFiles {
            guard currentCacheSize > targetSize else { break }
            
            try? fileManager.removeItem(at: fileURL)
            // Also remove metadata file
            let metadataURL = fileURL.appendingPathExtension("meta")
            try? fileManager.removeItem(at: metadataURL)
            
            currentCacheSize -= fileSize
        }
    }
    
    private func calculateDiskCacheSize() async -> Int {
        return await withCheckedContinuation { continuation in
            diskCacheQueue.async {
                let size = self.calculateDiskCacheSizeSync()
                continuation.resume(returning: size)
            }
        }
    }
    
    private func calculateDiskCacheSizeSync() -> Int {
        guard let fileURLs = try? fileManager.contentsOfDirectory(
            at: diskCacheURL,
            includingPropertiesForKeys: [.fileSizeKey],
            options: []
        ) else {
            return 0
        }
        
        return fileURLs.reduce(0) { total, fileURL in
            guard let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
                  let size = attributes[.fileSize] as? Int else {
                return total
            }
            return total + size
        }
    }
    
    // MARK: - Private Methods - Statistics
    
    nonisolated private func updateStatistics(memoryHit: Bool = false, diskHit: Bool = false, miss: Bool = false) {
        Task { @MainActor in
            if memoryHit {
                self.cacheStatistics.memoryHits += 1
            } else if diskHit {
                self.cacheStatistics.diskHits += 1
            } else if miss {
                self.cacheStatistics.misses += 1
            }
            self.cacheStatistics.totalRequests += 1
        }
    }
    
    // MARK: - Memory Warning
    
    #if os(iOS)
    @objc private func handleMemoryWarning() {
        // Clear memory cache during memory pressure
        memoryCache.removeAllObjects()
    }
    #endif
}

// MARK: - Supporting Types

/// Configuration for image cache
struct ImageCacheConfiguration {
    let memoryCountLimit: Int
    let memoryCostLimit: Int // in bytes
    let maxDiskCacheSize: Int // in bytes
    let maxAge: TimeInterval // in seconds
    let cleanupInterval: TimeInterval // in seconds
    let diskCompressionQuality: CGFloat
    
    static let `default` = ImageCacheConfiguration(
        memoryCountLimit: 100,
        memoryCostLimit: 50 * 1024 * 1024, // 50 MB
        maxDiskCacheSize: 200 * 1024 * 1024, // 200 MB
        maxAge: 7 * 24 * 60 * 60, // 7 days
        cleanupInterval: 60 * 60, // 1 hour
        diskCompressionQuality: 0.8
    )
}

/// Cache statistics
struct CacheStatistics {
    var memoryHits: Int = 0
    var diskHits: Int = 0
    var misses: Int = 0
    var totalRequests: Int = 0
    
    var hitRate: Double {
        guard totalRequests > 0 else { return 0 }
        return Double(memoryHits + diskHits) / Double(totalRequests)
    }
    
    var memoryHitRate: Double {
        guard totalRequests > 0 else { return 0 }
        return Double(memoryHits) / Double(totalRequests)
    }
}

/// Cache size information
struct CacheSizeInfo {
    let memorySize: Int
    let diskSize: Int
    let totalSize: Int
    
    var formattedMemorySize: String {
        ByteCountFormatter.string(fromByteCount: Int64(memorySize), countStyle: .file)
    }
    
    var formattedDiskSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(diskSize), countStyle: .file)
    }
    
    var formattedTotalSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(totalSize), countStyle: .file)
    }
}


// MARK: - SHA256 Implementation using CryptoKit

private extension String {
    var sha256: String {
        let data = Data(self.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
}