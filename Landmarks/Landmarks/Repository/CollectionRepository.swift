/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Repository for managing landmark collection data with support for user collections and favorites.
*/

import Foundation
import Combine

/// Repository for managing landmark collection data
final class CollectionRepository: BaseRepository<LandmarkCollection>, SearchableRepository {
    
    // MARK: - Private Properties
    
    private let landmarkRepository: LandmarkRepository
    private var favoritesCollection: LandmarkCollection?
    private var userCollections: [LandmarkCollection] = []
    
    // MARK: - Initialization
    
    init(landmarkRepository: LandmarkRepository, configuration: RepositoryConfiguration = .default) {
        self.landmarkRepository = landmarkRepository
        super.init(configuration: configuration)
    }
    
    // MARK: - RepositoryProtocol
    
    func fetchAll() async throws -> [LandmarkCollection] {
        updateState(.loading)
        
        do {
            // Load collections from static data (can be replaced with API call later)
            let collectionList = LandmarkCollection.exampleData
            
            // Populate collections with actual landmark objects
            for collection in collectionList {
                let landmarks = try await getLandmarks(for: collection.landmarkIds)
                collection.landmarks = landmarks
            }
            
            // Cache collections
            collectionList.forEach { cacheEntity($0) }
            
            // Separate favorites and user collections
            organizeFavoritesAndUserCollections(collectionList)
            
            // Update internal state
            updateEntities(collectionList)
            updateState(.loaded)
            
            return collectionList
        } catch {
            updateState(.error(error))
            throw error
        }
    }
    
    func fetch(by id: Int) async throws -> LandmarkCollection? {
        // Try cache first
        if let cachedCollection = getCachedEntity(by: id) {
            return cachedCollection
        }
        
        // If not in cache, fetch from main collection
        let allCollections = try await fetchAll()
        return allCollections.first { $0.id == id }
    }
    
    func save(_ collection: LandmarkCollection) async throws -> LandmarkCollection {
        // Update cache
        cacheEntity(collection)
        
        // Update entities array
        var updatedEntities = entities
        if let index = updatedEntities.firstIndex(where: { $0.id == collection.id }) {
            updatedEntities[index] = collection
        } else {
            updatedEntities.append(collection)
        }
        
        updateEntities(updatedEntities)
        organizeFavoritesAndUserCollections(updatedEntities)
        
        return collection
    }
    
    func delete(by id: Int) async throws {
        // Don't allow deletion of favorites collection
        guard id != 1001 else {
            throw CollectionRepositoryError.cannotDeleteFavorites
        }
        
        var updatedEntities = entities
        updatedEntities.removeAll { $0.id == id }
        
        updateEntities(updatedEntities)
        organizeFavoritesAndUserCollections(updatedEntities)
    }
    
    // MARK: - SearchableRepository
    
    func search(query: String) async throws -> [LandmarkCollection] {
        guard !query.isEmpty else {
            return entities
        }
        
        let lowercaseQuery = query.lowercased()
        return entities.filter { collection in
            String(localized: collection.name).lowercased().contains(lowercaseQuery) ||
            String(localized: collection.description).lowercased().contains(lowercaseQuery)
        }
    }
    
    func filter(_ predicate: @escaping (LandmarkCollection) -> Bool) async throws -> [LandmarkCollection] {
        return entities.filter(predicate)
    }
    
    // MARK: - Collection-specific Methods
    
    /// Gets the favorites collection
    func getFavoritesCollection() async throws -> LandmarkCollection {
        if let favorites = favoritesCollection {
            return favorites
        }
        
        let allCollections = try await fetchAll()
        guard let favorites = allCollections.first(where: { $0.id == 1001 }) else {
            throw CollectionRepositoryError.favoritesCollectionNotFound
        }
        
        return favorites
    }
    
    /// Gets user-created collections (excludes favorites)
    func getUserCollections() async throws -> [LandmarkCollection] {
        if userCollections.isEmpty {
            _ = try await fetchAll()
        }
        return userCollections
    }
    
    /// Creates a new user collection
    func createNewUserCollection() async throws -> LandmarkCollection {
        let nextId = getNextUserCollectionId()
        
        let newCollection = LandmarkCollection(
            id: nextId,
            name: String(localized: "New Collection"),
            description: String(localized: "Add a description for your collection here…"),
            landmarkIds: [],
            landmarks: []
        )
        
        return try await save(newCollection)
    }
    
    /// Checks if a collection contains a specific landmark
    func collectionContains(_ collection: LandmarkCollection, landmark: Landmark) -> Bool {
        return collection.landmarks.contains(landmark)
    }
    
    /// Gets all collections that contain a specific landmark
    func getCollectionsContaining(_ landmark: Landmark) async throws -> [LandmarkCollection] {
        let userCollections = try await getUserCollections()
        return userCollections.filter { collection in
            collectionContains(collection, landmark: landmark)
        }
    }
    
    /// Adds a landmark to a collection
    func addLandmark(_ landmark: Landmark, to collection: LandmarkCollection) async throws -> LandmarkCollection {
        // Check if landmark is already in collection
        guard !collectionContains(collection, landmark: landmark) else {
            return collection
        }
        
        // Add landmark to collection
        collection.landmarks.append(landmark)
        collection.landmarkIds.append(landmark.id)
        
        return try await save(collection)
    }
    
    /// Removes a landmark from a collection
    func removeLandmark(_ landmark: Landmark, from collection: LandmarkCollection) async throws -> LandmarkCollection {
        // Remove landmark from collection
        collection.landmarks.removeAll { $0.id == landmark.id }
        collection.landmarkIds.removeAll { $0 == landmark.id }
        
        return try await save(collection)
    }
    
    /// Toggles favorite status of a landmark
    func toggleFavorite(_ landmark: Landmark) async throws {
        let favoritesCollection = try await getFavoritesCollection()
        
        if collectionContains(favoritesCollection, landmark: landmark) {
            _ = try await removeLandmark(landmark, from: favoritesCollection)
        } else {
            _ = try await addLandmark(landmark, to: favoritesCollection)
        }
    }
    
    /// Checks if a landmark is favorited
    func isFavorite(_ landmark: Landmark) async throws -> Bool {
        let favoritesCollection = try await getFavoritesCollection()
        return collectionContains(favoritesCollection, landmark: landmark)
    }
    
    // MARK: - Private Methods
    
    private func organizeFavoritesAndUserCollections(_ collections: [LandmarkCollection]) {
        favoritesCollection = collections.first { $0.id == 1001 }
        userCollections = collections.filter { $0.id != 1001 }
    }
    
    private func getNextUserCollectionId() -> Int {
        let sortedUserCollections = userCollections.sorted { $0.id > $1.id }
        return (sortedUserCollections.first?.id ?? 1001) + 1
    }
    
    private func getLandmarks(for landmarkIds: [Int]) async throws -> [Landmark] {
        var landmarks: [Landmark] = []
        
        for landmarkId in landmarkIds {
            if let landmark = try await landmarkRepository.fetch(by: landmarkId) {
                landmarks.append(landmark)
            }
        }
        
        return landmarks
    }
}

// MARK: - CollectionRepository Errors

enum CollectionRepositoryError: Error, LocalizedError {
    case favoritesCollectionNotFound
    case cannotDeleteFavorites
    case landmarkNotFound(Int)
    
    var errorDescription: String? {
        switch self {
        case .favoritesCollectionNotFound:
            return "Favorites collection not found"
        case .cannotDeleteFavorites:
            return "Cannot delete the favorites collection"
        case .landmarkNotFound(let id):
            return "Landmark with ID \(id) not found"
        }
    }
}