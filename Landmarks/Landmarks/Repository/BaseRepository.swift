/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Base repository implementation providing common functionality for all repositories.
*/

import Foundation
import Combine

/// Base repository class providing common functionality
class BaseRepository<Entity: Identifiable>: ObservableObject {
    typealias ID = Entity.ID
    
    // MARK: - Properties
    
    @Published var state: RepositoryState = .idle
    @Published var entities: [Entity] = []
    
    internal let configuration: RepositoryConfiguration
    private let entitiesSubject = CurrentValueSubject<[Entity], Never>([])
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Cache
    
    private var cache: [ID: CachedEntity<Entity>] = [:]
    private let cacheQueue = DispatchQueue(label: "repository.cache", attributes: .concurrent)
    
    // MARK: - Initialization
    
    init(configuration: RepositoryConfiguration = .default) {
        self.configuration = configuration
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /// Updates the internal entities array and notifies observers
    internal func updateEntities(_ newEntities: [Entity]) {
        DispatchQueue.main.async {
            self.entities = newEntities
            self.entitiesSubject.send(newEntities)
        }
    }
    
    /// Updates the repository state
    internal func updateState(_ newState: RepositoryState) {
        DispatchQueue.main.async {
            self.state = newState
        }
    }
    
    /// Observes changes to entities
    func observe() -> AnyPublisher<[Entity], Never> {
        return entitiesSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Cache Management
    
    /// Caches an entity
    internal func cacheEntity(_ entity: Entity) {
        guard configuration.cacheEnabled else { return }
        
        cacheQueue.async(flags: .barrier) {
            self.cache[entity.id] = CachedEntity(
                entity: entity,
                timestamp: Date(),
                ttl: self.configuration.cacheTTL
            )
        }
    }
    
    /// Retrieves an entity from cache
    internal func getCachedEntity(by id: ID) -> Entity? {
        guard configuration.cacheEnabled else { return nil }
        
        return cacheQueue.sync {
            guard let cachedEntity = cache[id] else { return nil }
            
            // Check if cache is still valid
            if cachedEntity.isExpired {
                cache.removeValue(forKey: id)
                return nil
            }
            
            return cachedEntity.entity
        }
    }
    
    /// Clears the cache
    internal func clearCache() {
        cacheQueue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }
    
    /// Removes expired cache entries
    private func cleanupCache() {
        cacheQueue.async(flags: .barrier) {
            let now = Date()
            self.cache = self.cache.filter { !$0.value.isExpired(at: now) }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupObservers() {
        // Setup periodic cache cleanup
        Timer.publish(every: 60, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.cleanupCache()
            }
            .store(in: &cancellables)
    }
}

// MARK: - Cached Entity

private struct CachedEntity<Entity> {
    let entity: Entity
    let timestamp: Date
    let ttl: TimeInterval
    
    var isExpired: Bool {
        return isExpired(at: Date())
    }
    
    func isExpired(at date: Date) -> Bool {
        return date.timeIntervalSince(timestamp) > ttl
    }
}