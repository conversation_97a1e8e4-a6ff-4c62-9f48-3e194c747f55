/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
A preview view that demonstrates all typography styles and text processing features.
*/

import SwiftUI

/// A comprehensive preview view for the typography system, useful for development and testing.
struct TypographyPreview: View {
    @State private var sampleText = "The quick brown fox jumps over the lazy dog. 这是一个中文测试文本。هذا نص تجريبي باللغة العربية."
    @State private var longText = """
    This is a longer text sample that demonstrates how the typography system handles extended content. It includes multiple sentences and should show how line spacing, text wrapping, and readability features work together to create an optimal reading experience.
    
    The text processing system automatically handles different content types and applies appropriate formatting rules. This includes smart truncation, responsive scaling, and accessibility enhancements.
    """
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 24) {
                    
                    // Typography Styles Section
                    typographyStylesSection
                    
                    Divider()
                    
                    // Color Variations Section
                    colorVariationsSection
                    
                    Divider()
                    
                    // Text Processing Section
                    textProcessingSection
                    
                    Divider()
                    
                    // Responsive Design Section
                    responsiveDesignSection
                    
                    Divider()
                    
                    // Accessibility Features Section
                    accessibilitySection
                    
                    Divider()
                    
                    // Interactive Components Section
                    interactiveComponentsSection
                }
                .padding()
            }
            .navigationTitle("Typography Preview")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    private var typographyStylesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Typography Styles")
                .responsiveTypography(.title)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("Hero Title Style")
                    .responsiveTypography(.heroTitle)
                
                Text("Title Style")
                    .responsiveTypography(.title)
                
                Text("Subtitle Style")
                    .responsiveTypography(.subtitle)
                
                Text("Body Style - Regular weight for comfortable reading")
                    .responsiveTypography(.body)
                
                Text("Body Emphasized Style - Medium weight for emphasis")
                    .responsiveTypography(.bodyEmphasized)
                
                Text("Caption Style - Smaller text for secondary information")
                    .responsiveTypography(.caption)
                
                Text("Badge Style")
                    .responsiveTypography(.badge)
                
                Text("Button Style")
                    .responsiveTypography(.button)
            }
        }
    }
    
    private var colorVariationsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Color Variations")
                .responsiveTypography(.title)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Primary Text Color")
                    .responsiveTypography(.body, color: .primary)
                
                Text("Secondary Text Color")
                    .responsiveTypography(.body, color: .secondary)
                
                Text("Tertiary Text Color")
                    .responsiveTypography(.body, color: .tertiary)
                
                Text("Accent Text Color")
                    .responsiveTypography(.body, color: .accent)
                
                Text("Error Text Color")
                    .responsiveTypography(.body, color: .error)
                
                Text("Success Text Color")
                    .responsiveTypography(.body, color: .success)
                
                Text("Warning Text Color")
                    .responsiveTypography(.body, color: .warning)
            }
        }
    }
    
    private var textProcessingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Text Processing Features")
                .responsiveTypography(.title)
            
            VStack(alignment: .leading, spacing: 12) {
                Text("Title Content Type (3 lines max)")
                    .responsiveTypography(.subtitle)
                    .processedText(.title)
                
                Text("Subtitle Content Type (2 lines max)")
                    .responsiveTypography(.body)
                    .processedText(.subtitle)
                
                Text("Caption Content Type (2 lines max, not selectable)")
                    .responsiveTypography(.caption)
                    .processedText(.caption)
                
                EnhancedTextView(
                    text: longText,
                    contentType: .description,
                    isExpandable: true
                )
            }
        }
    }
    
    private var responsiveDesignSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Responsive Design")
                .responsiveTypography(.title)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Device Type: \(ResponsiveDesign.DeviceType.current.description)")
                    .responsiveTypography(.body)
                
                Text("Screen Size: \(ResponsiveDesign.ScreenSize.current.description)")
                    .responsiveTypography(.body)
                
                Text("Typography Scale - Hero: \(String(format: "%.1f", ResponsiveDesign.TypographyScale.current.heroTitle))")
                    .responsiveTypography(.caption)
                
                Text("Typography Scale - Body: \(String(format: "%.1f", ResponsiveDesign.TypographyScale.current.body))")
                    .responsiveTypography(.caption)
            }
        }
    }
    
    private var accessibilitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Accessibility Features")
                .responsiveTypography(.title)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("High Contrast: \(ColorSystem.isHighContrastEnabled ? "Enabled" : "Disabled")")
                    .responsiveTypography(.body)
                
                Text("Reduce Transparency: \(ColorSystem.isReduceTransparencyEnabled ? "Enabled" : "Disabled")")
                    .responsiveTypography(.body)
                
                Text("This text demonstrates accessibility-enhanced styling")
                    .typographyAccessible(.body)
                    .accessibilityEnhanced()
            }
        }
    }
    
    private var interactiveComponentsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Interactive Components")
                .responsiveTypography(.title)
            
            VStack(alignment: .leading, spacing: 12) {
                EnhancedTextEditor(
                    text: $sampleText,
                    placeholder: "Enter sample text...",
                    contentType: .body
                )
                
                SmartTextContainer(
                    text: $longText,
                    isEditable: false,
                    contentType: .description
                )
            }
        }
    }
}

// MARK: - Extensions for Preview

extension ResponsiveDesign.DeviceType {
    var description: String {
        switch self {
        case .iPhone: return "iPhone"
        case .iPadCompact: return "iPad Compact"
        case .iPadRegular: return "iPad Regular"
        case .mac: return "Mac"
        }
    }
}

extension ResponsiveDesign.ScreenSize {
    var description: String {
        switch self {
        case .compact: return "Compact"
        case .regular: return "Regular"
        case .large: return "Large"
        }
    }
}

#Preview {
    TypographyPreview()
}

#Preview("Dark Mode") {
    TypographyPreview()
        .preferredColorScheme(.dark)
}

#Preview("Large Text") {
    TypographyPreview()
        .environment(\.sizeCategory, .accessibilityExtraExtraExtraLarge)
}
