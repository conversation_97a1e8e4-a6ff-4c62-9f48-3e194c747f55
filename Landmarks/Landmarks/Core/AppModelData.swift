/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Refactored ModelData using the new architecture with repositories, services, and error handling.
*/

import Foundation
import SwiftUI
import MapKit
import CoreLocation
import Combine

// Type alias for backward compatibility
typealias Continent = AppModelData.Continent

/// Refactored model data class using the new architecture
@Observable @MainActor
final class AppModelData {
    
    // MARK: - Dependencies
    
    private let landmarkRepository: LandmarkRepository
    private let collectionRepository: CollectionRepository
    private let errorHandler: ErrorHandler
    private let userPreferencesService: UserPreferencesService
    private let responsiveDesignCache: ResponsiveDesignCache
    
    // MARK: - Published State
    
    var landmarks: [Landmark] = []
    var landmarksByContinent: [Continent: [Landmark]] = [:]
    var featuredLandmark: Landmark?
    var selectedLandmark: Landmark? = nil
    var isLandmarkInspectorPresented: Bool = false
    
    var favoritesCollection: LandmarkCollection?
    var userCollections: [LandmarkCollection] = []
    
    var landmarksById: [Int: Landmark] = [:]
    var mapItemsByLandmarkId: [Int: MKMapItem] = [:]
    
    var searchString: String = ""
    var path: NavigationPath = NavigationPath() {
        didSet {
            // Check if the person navigates away from a view that's showing the inspector.
            if path.count < oldValue.count && isLandmarkInspectorPresented == true {
                // Dismiss the inspector.
                isLandmarkInspectorPresented = false
            }
        }
    }
    
    var windowSize: CGSize = .zero
    
    // MARK: - Loading States
    
    var isLoadingLandmarks: Bool = false
    var isLoadingCollections: Bool = false
    var isLoadingMapItems: Bool = false
    
    // MARK: - Error State
    
    var currentError: AppError? {
        errorHandler.currentError
    }
    
    // MARK: - Initialization
    
    init(
        landmarkRepository: LandmarkRepository? = nil,
        collectionRepository: CollectionRepository? = nil,
        errorHandler: ErrorHandler = ErrorHandler(),
        userPreferencesService: UserPreferencesService = UserPreferencesService(),
        responsiveDesignCache: ResponsiveDesignCache = ResponsiveDesignCache.shared
    ) {
        // Initialize dependencies
        self.errorHandler = errorHandler
        self.userPreferencesService = userPreferencesService
        self.responsiveDesignCache = responsiveDesignCache
        
        // Initialize repositories with dependency injection
        let mapService = MapService()
        self.landmarkRepository = landmarkRepository ?? LandmarkRepository(mapService: mapService)
        self.collectionRepository = collectionRepository ?? CollectionRepository(landmarkRepository: self.landmarkRepository)
        
        // Start loading data
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - Data Loading
    
    /// Loads all initial data
    private func loadInitialData() async {
        await withTaskGroup(of: Void.self) { group in
            // Load landmarks
            group.addTask {
                await self.loadLandmarks()
            }
            
            // Load collections
            group.addTask {
                await self.loadCollections()
            }
        }
        
        // Load map items after landmarks are loaded
        await loadMapItems()
    }
    
    /// Loads landmarks from repository
    private func loadLandmarks() async {
        isLoadingLandmarks = true
        
        do {
            let loadedLandmarks = try await landmarkRepository.fetchAll()
            
            // Update state
            landmarks = loadedLandmarks
            landmarksByContinent = landmarkRepository.getLandmarksByContinent()
            
            // Build landmarks by ID dictionary
            landmarksById = Dictionary(uniqueKeysWithValues: landmarks.map { ($0.id, $0) })
            
            // Set featured landmark
            featuredLandmark = try await landmarkRepository.getFeaturedLandmark()
            
            isLoadingLandmarks = false
        } catch {
            isLoadingLandmarks = false
            errorHandler.handle(error)
        }
    }
    
    /// Loads collections from repository
    private func loadCollections() async {
        isLoadingCollections = true
        
        do {
            let allCollections = try await collectionRepository.fetchAll()
            
            // Separate favorites and user collections
            favoritesCollection = try await collectionRepository.getFavoritesCollection()
            userCollections = try await collectionRepository.getUserCollections()
            
            isLoadingCollections = false
        } catch {
            isLoadingCollections = false
            errorHandler.handle(error)
        }
    }
    
    /// Loads map items for landmarks
    private func loadMapItems() async {
        guard !landmarks.isEmpty else { return }
        
        isLoadingMapItems = true
        
        do {
            let mapItems = try await landmarkRepository.fetchMapItems(for: landmarks)
            mapItemsByLandmarkId = mapItems
            isLoadingMapItems = false
        } catch {
            isLoadingMapItems = false
            errorHandler.handle(error, shouldPresentToUser: false) // Don't show map errors to user
        }
    }
    
    // MARK: - Landmark Operations
    
    /// Gets landmarks for a specific continent
    func landmarks(in continent: Continent) async -> [Landmark] {
        do {
            return try await landmarkRepository.getLandmarks(for: continent)
        } catch {
            errorHandler.handle(error)
            return []
        }
    }
    
    /// Searches landmarks
    func searchLandmarks(query: String) async -> [Landmark] {
        do {
            return try await landmarkRepository.search(query: query)
        } catch {
            errorHandler.handle(error)
            return []
        }
    }
    
    // MARK: - Collection Operations
    
    /// Checks if a landmark is favorited
    func isFavorite(_ landmark: Landmark) async -> Bool {
        do {
            return try await collectionRepository.isFavorite(landmark)
        } catch {
            errorHandler.handle(error)
            return false
        }
    }
    
    /// Toggles favorite status of a landmark
    func toggleFavorite(_ landmark: Landmark) async {
        do {
            try await collectionRepository.toggleFavorite(landmark)
            
            // Refresh favorites collection
            favoritesCollection = try await collectionRepository.getFavoritesCollection()
        } catch {
            errorHandler.handle(error)
        }
    }
    
    /// Adds a new user collection
    func addUserCollection() async -> LandmarkCollection? {
        do {
            let newCollection = try await collectionRepository.createNewUserCollection()
            userCollections.append(newCollection)
            return newCollection
        } catch {
            errorHandler.handle(error)
            return nil
        }
    }
    
    /// Removes a user collection
    func remove(_ collection: LandmarkCollection) async {
        do {
            try await collectionRepository.delete(by: collection.id)
            userCollections.removeAll { $0.id == collection.id }
        } catch {
            errorHandler.handle(error)
        }
    }
    
    /// Checks if a collection contains a landmark
    func collection(_ collection: LandmarkCollection, contains landmark: Landmark) -> Bool {
        return collectionRepository.collectionContains(collection, landmark: landmark)
    }
    
    /// Gets collections containing a landmark
    func collectionsContaining(_ landmark: Landmark) async -> [LandmarkCollection] {
        do {
            return try await collectionRepository.getCollectionsContaining(landmark)
        } catch {
            errorHandler.handle(error)
            return []
        }
    }
    
    /// Adds a landmark to a collection
    func add(_ landmark: Landmark, to collection: LandmarkCollection) async {
        do {
            _ = try await collectionRepository.addLandmark(landmark, to: collection)
            
            // Refresh collections
            userCollections = try await collectionRepository.getUserCollections()
        } catch {
            errorHandler.handle(error)
        }
    }
    
    /// Removes a landmark from a collection
    func remove(_ landmark: Landmark, from collection: LandmarkCollection) async {
        do {
            _ = try await collectionRepository.removeLandmark(landmark, from: collection)
            
            // Refresh collections
            userCollections = try await collectionRepository.getUserCollections()
            
            // Refresh favorites if needed
            if collection.id == 1001 {
                favoritesCollection = try await collectionRepository.getFavoritesCollection()
            }
        } catch {
            errorHandler.handle(error)
        }
    }
    
    // MARK: - Map Operations
    
    /// Gets map items for landmarks
    var mapItemsForLandmarks: [MKMapItem] {
        return Array(mapItemsByLandmarkId.values)
    }
    
    // MARK: - Computed Properties
    
    /// Gets earned badges
    var earnedBadges: [Badge] {
        let badges = landmarks.compactMap { landmark in
            if landmark.badge != nil,
                let progress = landmark.badgeProgress,
                progress.earned == true {
                return landmark.badge
            }
            return nil
        }
        return badges
    }
    
    // MARK: - User Preferences
    
    /// Gets a user preference value
    func getPreference<T>(for key: PreferenceKey<T>) -> T {
        return userPreferencesService.getValue(for: key)
    }
    
    /// Sets a user preference value
    func setPreference<T>(_ value: T, for key: PreferenceKey<T>) {
        userPreferencesService.setValue(value, for: key)
    }
    
    // MARK: - Error Handling
    
    /// Dismisses the current error
    func dismissError() {
        errorHandler.dismissCurrentError()
    }
    
    /// Retries the last failed operation
    func retryLastOperation() {
        errorHandler.retryLastOperation()
    }
    
    // MARK: - Cache Management
    
    /// Clears all caches
    func clearCaches() {
        ImageCache.shared.clearCache()
        responsiveDesignCache.clearCache()
    }
    
    /// Refreshes all data
    func refreshAllData() async {
        await loadInitialData()
    }
}

// MARK: - Continent Definition

extension AppModelData {
    enum Continent: String, CaseIterable {
        case africa = "Africa"
        case antarctica = "Antarctica"
        case asia = "Asia"
        case australiaOceania = "Australia/Oceania"
        case europe = "Europe"
        case northAmerica = "North America"
        case southAmerica = "South America"
        
        var name: String {
            switch self {
            case .africa: String(localized: "Africa", comment: "The name of a continent.")
            case .antarctica: String(localized: "Antarctica", comment: "The name of a continent.")
            case .asia: String(localized: "Asia", comment: "The name of a continent.")
            case .australiaOceania: String(localized: "Australia/Oceania", comment: "The name of a continent.")
            case .europe: String(localized: "Europe", comment: "The name of a continent.")
            case .northAmerica: String(localized: "North America", comment: "The name of a continent.")
            case .southAmerica: String(localized: "South America", comment: "The name of a continent.")
            }
        }
    }
    
    static let orderedContinents: [Continent] = [.asia, .africa, .antarctica, .australiaOceania, .europe, .northAmerica, .southAmerica]
}