/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Centralized error handling system for managing and presenting errors to users.
*/

import Foundation
import SwiftUI
import Combine

/// Centralized error handler for the application
@MainActor
final class ErrorHandler: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentError: AppError?
    @Published var errorHistory: [ErrorEntry] = []
    @Published var isShowingError: Bool = false
    
    // MARK: - Private Properties
    
    private let maxHistoryCount = 50
    private var errorAnalytics: [String: Int] = [:]
    private let analyticsQueue = DispatchQueue(label: "errorhandler.analytics", attributes: .concurrent)
    
    // MARK: - Configuration
    
    private let configuration: ErrorHandlerConfiguration
    
    // MARK: - Initialization
    
    init(configuration: ErrorHandlerConfiguration = .default) {
        self.configuration = configuration
    }
    
    // MARK: - Public Methods
    
    /// Handles an error with optional user presentation
    func handle(_ error: Error, shouldPresentToUser: Bool = true) {
        let appError = AppError.from(error)
        
        // Log error
        logError(appError)
        
        // Add to history
        addToHistory(appError)
        
        // Update analytics
        updateAnalytics(for: appError)
        
        // Present to user if required
        if shouldPresentToUser && appError.shouldShowToUser {
            presentError(appError)
        }
        
        // Send to crash reporting if configured
        if configuration.enableCrashReporting && appError.severity == .high {
            reportToCrashlytics(appError)
        }
    }
    
    /// Handles an error with a custom presentation method
    func handle(_ error: Error, presentationMethod: ErrorPresentationMethod) {
        let appError = AppError.from(error)
        
        // Log and track error
        logError(appError)
        addToHistory(appError)
        updateAnalytics(for: appError)
        
        // Present using specified method
        switch presentationMethod {
        case .alert:
            presentError(appError)
        case .toast:
            presentToast(appError)
        case .banner:
            presentBanner(appError)
        case .silent:
            break // Just log, don't present
        case .custom(let handler):
            handler(appError)
        }
    }
    
    /// Dismisses the current error
    func dismissCurrentError() {
        currentError = nil
        isShowingError = false
    }
    
    /// Clears error history
    func clearHistory() {
        errorHistory.removeAll()
    }
    
    /// Gets error analytics
    func getErrorAnalytics() -> [String: Int] {
        return analyticsQueue.sync {
            return errorAnalytics
        }
    }
    
    /// Retries the last failed operation if a retry handler was provided
    func retryLastOperation() {
        guard let lastEntry = errorHistory.last,
              let retryHandler = lastEntry.retryHandler else {
            return
        }
        
        Task {
            do {
                try await retryHandler()
                // If successful, remove the error from current display
                if currentError?.id == lastEntry.error.id {
                    dismissCurrentError()
                }
            } catch {
                // If retry fails, handle the new error
                handle(error)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func presentError(_ error: AppError) {
        currentError = error
        isShowingError = true
    }
    
    private func presentToast(_ error: AppError) {
        // For toast presentation, we'll use a different property
        // This would integrate with a toast system
        NotificationCenter.default.post(
            name: .showToast,
            object: error
        )
    }
    
    private func presentBanner(_ error: AppError) {
        // For banner presentation
        NotificationCenter.default.post(
            name: .showBanner,
            object: error
        )
    }
    
    private func logError(_ error: AppError) {
        let timestamp = Date()
        let message = "[\(timestamp)] \(error.severity.description): \(error.errorDescription ?? "Unknown error")"
        
        if configuration.enableConsoleLogging {
            print("🔴 \(message)")
        }
        
        if configuration.enableFileLogging {
            writeToLogFile(message)
        }
    }
    
    private func addToHistory(_ error: AppError) {
        let entry = ErrorEntry(
            error: error,
            timestamp: Date(),
            retryHandler: nil
        )
        
        errorHistory.append(entry)
        
        // Trim history if it exceeds max count
        if errorHistory.count > maxHistoryCount {
            errorHistory.removeFirst(errorHistory.count - maxHistoryCount)
        }
    }
    
    private func updateAnalytics(for error: AppError) {
        analyticsQueue.async(flags: .barrier) {
            let key = error.id
            self.errorAnalytics[key, default: 0] += 1
        }
    }
    
    private func writeToLogFile(_ message: String) {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        
        let logFileURL = documentsPath.appendingPathComponent("error_log.txt")
        
        do {
            let logEntry = "\(message)\n"
            if FileManager.default.fileExists(atPath: logFileURL.path) {
                let fileHandle = try FileHandle(forWritingTo: logFileURL)
                fileHandle.seekToEndOfFile()
                fileHandle.write(logEntry.data(using: .utf8) ?? Data())
                fileHandle.closeFile()
            } else {
                try logEntry.write(to: logFileURL, atomically: true, encoding: .utf8)
            }
        } catch {
            print("Failed to write to log file: \(error)")
        }
    }
    
    private func reportToCrashlytics(_ error: AppError) {
        // This would integrate with Firebase Crashlytics or similar
        // For now, we'll just print a message
        print("📊 Reporting to crashlytics: \(error.errorDescription ?? "Unknown error")")
    }
}

// MARK: - Supporting Types

/// Configuration for error handler behavior
struct ErrorHandlerConfiguration {
    let enableConsoleLogging: Bool
    let enableFileLogging: Bool
    let enableCrashReporting: Bool
    let enableAnalytics: Bool
    let autoRetryNetworkErrors: Bool
    let maxRetryAttempts: Int
    
    static let `default` = ErrorHandlerConfiguration(
        enableConsoleLogging: true,
        enableFileLogging: false,
        enableCrashReporting: false,
        enableAnalytics: true,
        autoRetryNetworkErrors: false,
        maxRetryAttempts: 3
    )
}

/// Error entry for history tracking
struct ErrorEntry: Identifiable {
    let id = UUID()
    let error: AppError
    let timestamp: Date
    let retryHandler: (() async throws -> Void)?
}

/// Error presentation methods
enum ErrorPresentationMethod {
    case alert
    case toast
    case banner
    case silent
    case custom((AppError) -> Void)
}

// MARK: - Notification Names

extension Notification.Name {
    static let showToast = Notification.Name("showToast")
    static let showBanner = Notification.Name("showBanner")
}

// MARK: - ErrorSeverity Extension

extension ErrorSeverity {
    var description: String {
        switch self {
        case .low:
            return "INFO"
        case .medium:
            return "WARNING"
        case .high:
            return "ERROR"
        }
    }
}

// MARK: - SwiftUI Integration

/// View modifier for handling errors
struct ErrorHandlingModifier: ViewModifier {
    @ObservedObject var errorHandler: ErrorHandler
    
    func body(content: Content) -> some View {
        content
            .alert("Error", isPresented: $errorHandler.isShowingError, presenting: errorHandler.currentError) { error in
                Button("OK") {
                    errorHandler.dismissCurrentError()
                }
                
                if errorHandler.errorHistory.last?.retryHandler != nil {
                    Button("Retry") {
                        errorHandler.retryLastOperation()
                    }
                }
            } message: { error in
                VStack(alignment: .leading) {
                    Text(error.errorDescription ?? "An unknown error occurred")
                    
                    if let suggestion = error.recoverySuggestion {
                        Text(suggestion)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
    }
}

extension View {
    /// Adds error handling to a view
    func errorHandling(_ errorHandler: ErrorHandler) -> some View {
        modifier(ErrorHandlingModifier(errorHandler: errorHandler))
    }
}