/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
A view that adds a gradient over an image to aid legibility for a text overlay.
*/

import SwiftUI

/// A view that adds a gradient over an image to improve legibility for a text overlay.
struct ReadabilityRoundedRectangle: View {
    /// The intensity of the gradient overlay (0.0 to 1.0)
    var intensity: Double = 0.8
    /// The height percentage of the gradient (0.0 to 1.0)
    var gradientHeight: Double = 0.6

    var body: some View {
        RoundedRectangle(cornerRadius: Constants.cornerRadius)
            .foregroundStyle(.clear)
            .background(
                LinearGradient(
                    colors: [
                        ColorSystem.accessibleOverlay(.readabilityGradientStart).opacity(intensity),
                        ColorSystem.accessibleOverlay(.readabilityGradientMid).opacity(intensity),
                        ColorSystem.OverlayColor.readabilityGradientEnd.color
                    ],
                    startPoint: .bottom,
                    endPoint: UnitPoint(x: 0.5, y: 1.0 - gradientHeight)
                )
            )
            .containerRelativeFrame(.vertical)
            .clipped()
    }
}

/// Enhanced readability overlay with customizable options
struct EnhancedReadabilityOverlay: View {
    var intensity: Double = 0.8
    var gradientHeight: Double = 0.6
    var cornerRadius: CGFloat = Constants.cornerRadius

    var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .foregroundStyle(.clear)
            .background(
                ZStack {
                    // Primary gradient for text readability
                    LinearGradient(
                        colors: [
                            ColorSystem.accessibleOverlay(.readabilityGradientStart).opacity(intensity),
                            ColorSystem.accessibleOverlay(.readabilityGradientMid).opacity(intensity),
                            ColorSystem.OverlayColor.readabilityGradientEnd.color
                        ],
                        startPoint: .bottom,
                        endPoint: UnitPoint(x: 0.5, y: 1.0 - gradientHeight)
                    )

                    // Subtle vignette effect for enhanced focus
                    RadialGradient(
                        colors: [
                            ColorSystem.OverlayColor.readabilityGradientEnd.color,
                            ColorSystem.accessibleOverlay(.vignetteOverlay)
                        ],
                        center: .center,
                        startRadius: 100,
                        endRadius: 300
                    )
                }
            )
            .containerRelativeFrame(.vertical)
            .clipped()
    }
}

