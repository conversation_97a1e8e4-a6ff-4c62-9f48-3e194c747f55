/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
UI tests for typography system to ensure proper text rendering and accessibility.
*/

import XCTest

final class TypographyUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - Text Rendering Tests
    
    func testLandmarkDetailTextRendering() throws {
        // Navigate to a landmark detail view
        app.tabBars.buttons["Landmarks"].tap()
        
        // Wait for the landmarks view to load
        let landmarksList = app.scrollViews.firstMatch
        XCTAssertTrue(landmarksList.waitForExistence(timeout: 5))
        
        // Tap on the first landmark
        let firstLandmark = landmarksList.buttons.firstMatch
        XCTAssertTrue(firstLandmark.waitForExistence(timeout: 5))
        firstLandmark.tap()
        
        // Verify that the landmark title is displayed
        let landmarkTitle = app.staticTexts.matching(identifier: "landmarkTitle").firstMatch
        XCTAssertTrue(landmarkTitle.waitForExistence(timeout: 5))
        XCTAssertFalse(landmarkTitle.label.isEmpty)
        
        // Verify that the description text is displayed and selectable
        let descriptionText = app.textViews.matching(identifier: "landmarkDescription").firstMatch
        XCTAssertTrue(descriptionText.waitForExistence(timeout: 5))
        XCTAssertFalse(descriptionText.value as? String ?? "" == "")
    }
    
    func testCollectionTextRendering() throws {
        // Navigate to collections
        app.tabBars.buttons["Collections"].tap()
        
        // Wait for collections view to load
        let collectionsView = app.scrollViews.firstMatch
        XCTAssertTrue(collectionsView.waitForExistence(timeout: 5))
        
        // Verify section titles are displayed
        let favoritesTitle = app.staticTexts["Favorites"]
        XCTAssertTrue(favoritesTitle.exists)
        
        let myCollectionsTitle = app.staticTexts["My Collections"]
        XCTAssertTrue(myCollectionsTitle.exists)
        
        // Tap on a collection
        let firstCollection = collectionsView.buttons.firstMatch
        if firstCollection.exists {
            firstCollection.tap()
            
            // Verify collection detail text elements
            let collectionTitle = app.staticTexts.matching(identifier: "collectionTitle").firstMatch
            XCTAssertTrue(collectionTitle.waitForExistence(timeout: 5))
        }
    }
    
    func testFeaturedLandmarkTextRendering() throws {
        // Navigate to landmarks view
        app.tabBars.buttons["Landmarks"].tap()
        
        // Wait for the view to load
        let landmarksView = app.scrollViews.firstMatch
        XCTAssertTrue(landmarksView.waitForExistence(timeout: 5))
        
        // Verify featured landmark text is displayed
        let featuredText = app.staticTexts["Featured Landmark"]
        XCTAssertTrue(featuredText.exists)
        
        // Verify featured landmark title is displayed
        let featuredLandmark = landmarksView.buttons.firstMatch
        XCTAssertTrue(featuredLandmark.exists)
    }
    
    // MARK: - Text Editing Tests
    
    func testTextEditorFunctionality() throws {
        // Navigate to collections
        app.tabBars.buttons["Collections"].tap()
        
        // Create a new collection
        let addButton = app.navigationBars.buttons["Add"]
        if addButton.exists {
            addButton.tap()
            
            // Wait for the editing view
            let editingView = app.scrollViews.firstMatch
            XCTAssertTrue(editingView.waitForExistence(timeout: 5))
            
            // Test name field
            let nameField = app.textFields.firstMatch
            if nameField.exists {
                nameField.tap()
                nameField.typeText("Test Collection")
                XCTAssertEqual(nameField.value as? String, "Test Collection")
            }
            
            // Test description editor
            let descriptionEditor = app.textViews.firstMatch
            if descriptionEditor.exists {
                descriptionEditor.tap()
                descriptionEditor.typeText("This is a test description for the collection.")
                
                // Verify text was entered
                let enteredText = descriptionEditor.value as? String ?? ""
                XCTAssertTrue(enteredText.contains("test description"))
            }
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testTextAccessibility() throws {
        // Navigate to landmarks
        app.tabBars.buttons["Landmarks"].tap()
        
        // Wait for view to load
        let landmarksView = app.scrollViews.firstMatch
        XCTAssertTrue(landmarksView.waitForExistence(timeout: 5))
        
        // Test that text elements have accessibility labels
        let featuredText = app.staticTexts["Featured Landmark"]
        XCTAssertTrue(featuredText.isAccessibilityElement)
        XCTAssertFalse(featuredText.accessibilityLabel?.isEmpty ?? true)
        
        // Navigate to a landmark detail
        let firstLandmark = landmarksView.buttons.firstMatch
        if firstLandmark.exists {
            firstLandmark.tap()
            
            // Test landmark title accessibility
            let landmarkTitle = app.staticTexts.matching(identifier: "landmarkTitle").firstMatch
            if landmarkTitle.waitForExistence(timeout: 5) {
                XCTAssertTrue(landmarkTitle.isAccessibilityElement)
                XCTAssertFalse(landmarkTitle.accessibilityLabel?.isEmpty ?? true)
            }
        }
    }
    
    func testVoiceOverCompatibility() throws {
        // This test would be more comprehensive with actual VoiceOver testing
        // For now, we'll test basic accessibility properties
        
        app.tabBars.buttons["Landmarks"].tap()
        
        let landmarksView = app.scrollViews.firstMatch
        XCTAssertTrue(landmarksView.waitForExistence(timeout: 5))
        
        // Test that interactive elements are accessible
        let featuredLandmark = landmarksView.buttons.firstMatch
        XCTAssertTrue(featuredLandmark.isAccessibilityElement)
        XCTAssertTrue(featuredLandmark.isEnabled)
    }
    
    // MARK: - Responsive Design Tests
    
    func testTextScalingOnDifferentSizes() throws {
        // This test would ideally test on different device sizes
        // For now, we'll test that text elements exist and are readable
        
        app.tabBars.buttons["Landmarks"].tap()
        
        let landmarksView = app.scrollViews.firstMatch
        XCTAssertTrue(landmarksView.waitForExistence(timeout: 5))
        
        // Navigate to landmark detail
        let firstLandmark = landmarksView.buttons.firstMatch
        if firstLandmark.exists {
            firstLandmark.tap()
            
            // Verify text elements are visible and have reasonable sizes
            let landmarkTitle = app.staticTexts.matching(identifier: "landmarkTitle").firstMatch
            if landmarkTitle.waitForExistence(timeout: 5) {
                let titleFrame = landmarkTitle.frame
                XCTAssertGreaterThan(titleFrame.height, 20) // Minimum readable height
                XCTAssertGreaterThan(titleFrame.width, 50)  // Minimum readable width
            }
        }
    }
    
    // MARK: - Text Truncation Tests
    
    func testLongTextHandling() throws {
        // Navigate to collections and test long text handling
        app.tabBars.buttons["Collections"].tap()
        
        let collectionsView = app.scrollViews.firstMatch
        XCTAssertTrue(collectionsView.waitForExistence(timeout: 5))
        
        // Look for collection items with potentially long names
        let collectionItems = collectionsView.buttons
        for i in 0..<min(collectionItems.count, 3) {
            let item = collectionItems.element(boundBy: i)
            if item.exists {
                // Verify the item is visible and text is not cut off inappropriately
                XCTAssertTrue(item.isHittable)
                
                // Check that the frame is reasonable
                let itemFrame = item.frame
                XCTAssertGreaterThan(itemFrame.height, 30)
                XCTAssertGreaterThan(itemFrame.width, 50)
            }
        }
    }
    
    // MARK: - Performance Tests
    
    func testTextRenderingPerformance() throws {
        measure {
            // Navigate through different views to test text rendering performance
            app.tabBars.buttons["Landmarks"].tap()
            
            let landmarksView = app.scrollViews.firstMatch
            _ = landmarksView.waitForExistence(timeout: 5)
            
            // Scroll through the view to trigger text rendering
            landmarksView.swipeUp()
            landmarksView.swipeDown()
            
            // Navigate to collections
            app.tabBars.buttons["Collections"].tap()
            
            let collectionsView = app.scrollViews.firstMatch
            _ = collectionsView.waitForExistence(timeout: 5)
        }
    }
    
    // MARK: - Dark Mode Tests
    
    func testDarkModeTextContrast() throws {
        // This test would ideally toggle dark mode and verify text contrast
        // For now, we'll test that text is visible in the current mode
        
        app.tabBars.buttons["Landmarks"].tap()
        
        let landmarksView = app.scrollViews.firstMatch
        XCTAssertTrue(landmarksView.waitForExistence(timeout: 5))
        
        // Navigate to landmark detail
        let firstLandmark = landmarksView.buttons.firstMatch
        if firstLandmark.exists {
            firstLandmark.tap()
            
            // Verify text elements are visible (indicating proper contrast)
            let landmarkTitle = app.staticTexts.matching(identifier: "landmarkTitle").firstMatch
            if landmarkTitle.waitForExistence(timeout: 5) {
                XCTAssertTrue(landmarkTitle.isHittable)
            }
            
            let descriptionText = app.textViews.matching(identifier: "landmarkDescription").firstMatch
            if descriptionText.waitForExistence(timeout: 5) {
                XCTAssertTrue(descriptionText.isHittable)
            }
        }
    }
}
