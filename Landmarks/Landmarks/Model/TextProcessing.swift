/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
A comprehensive text processing system for handling long text, truncation, and text formatting.
*/

import SwiftUI

/// A comprehensive text processing system that handles long text, truncation, and formatting.
struct TextProcessing {
    
    // MARK: - Text Truncation
    
    /// Text truncation strategies
    enum TruncationStrategy {
        case none
        case head
        case middle
        case tail
        case word
        case character
        
        /// Returns the appropriate SwiftUI truncation mode
        var truncationMode: Text.TruncationMode {
            switch self {
            case .none:
                return .tail // Default fallback
            case .head:
                return .head
            case .middle:
                return .middle
            case .tail, .word, .character:
                return .tail
            }
        }
    }
    
    // MARK: - Text Wrapping
    
    /// Text wrapping strategies for different content types
    enum WrappingStrategy {
        case automatic      // Let system handle wrapping
        case wordWrap       // Wrap at word boundaries
        case characterWrap  // Wrap at character boundaries
        case noWrap         // Prevent wrapping
        
        /// Returns the appropriate line limit
        var lineLimit: Int? {
            switch self {
            case .automatic:
                return nil
            case .wordWrap, .characterWrap:
                return nil
            case .noWrap:
                return 1
            }
        }
    }
    
    // MARK: - Text Formatting
    
    /// Smart text formatting for different content types
    enum ContentType {
        case title
        case subtitle
        case body
        case description
        case caption
        case quote
        case code
        
        /// Returns optimal formatting configuration
        var configuration: TextConfiguration {
            switch self {
            case .title:
                return TextConfiguration(
                    maxLines: 3,
                    truncation: .tail,
                    wrapping: .wordWrap,
                    allowSelection: false,
                    optimizeForReadability: true
                )
            case .subtitle:
                return TextConfiguration(
                    maxLines: 2,
                    truncation: .tail,
                    wrapping: .wordWrap,
                    allowSelection: false,
                    optimizeForReadability: true
                )
            case .body:
                return TextConfiguration(
                    maxLines: nil,
                    truncation: .none,
                    wrapping: .wordWrap,
                    allowSelection: true,
                    optimizeForReadability: true
                )
            case .description:
                return TextConfiguration(
                    maxLines: nil,
                    truncation: .none,
                    wrapping: .wordWrap,
                    allowSelection: true,
                    optimizeForReadability: true
                )
            case .caption:
                return TextConfiguration(
                    maxLines: 2,
                    truncation: .tail,
                    wrapping: .wordWrap,
                    allowSelection: false,
                    optimizeForReadability: false
                )
            case .quote:
                return TextConfiguration(
                    maxLines: nil,
                    truncation: .none,
                    wrapping: .wordWrap,
                    allowSelection: true,
                    optimizeForReadability: true
                )
            case .code:
                return TextConfiguration(
                    maxLines: nil,
                    truncation: .none,
                    wrapping: .characterWrap,
                    allowSelection: true,
                    optimizeForReadability: false
                )
            }
        }
    }
    
    // MARK: - Text Configuration
    
    /// Configuration for text display and behavior
    struct TextConfiguration {
        let maxLines: Int?
        let truncation: TruncationStrategy
        let wrapping: WrappingStrategy
        let allowSelection: Bool
        let optimizeForReadability: Bool
        
        /// Minimum scale factor for text that needs to fit
        var minimumScaleFactor: CGFloat {
            return maxLines != nil ? 0.8 : 1.0
        }
        
        /// Whether to use fixed size for vertical layout
        var useFixedSize: Bool {
            return maxLines == nil && wrapping != .noWrap
        }
    }
    
    // MARK: - Text Utilities
    
    /// Estimates the number of lines for given text and width
    static func estimateLineCount(for text: String, width: CGFloat, font: Font) -> Int {
        // This is a simplified estimation
        // In a real implementation, you might use NSString's boundingRect methods
        let averageCharacterWidth: CGFloat = 8.0 // Approximate
        let charactersPerLine = Int(width / averageCharacterWidth)
        return max(1, text.count / charactersPerLine)
    }
    
    /// Truncates text to a specific character count with smart word boundaries
    static func smartTruncate(_ text: String, to maxLength: Int, strategy: TruncationStrategy = .word) -> String {
        guard text.count > maxLength else { return text }
        
        switch strategy {
        case .none:
            return text
        case .head:
            let endIndex = text.index(text.startIndex, offsetBy: maxLength)
            return "..." + String(text[endIndex...])
        case .middle:
            let halfLength = maxLength / 2
            let startPart = String(text.prefix(halfLength))
            let endPart = String(text.suffix(halfLength))
            return startPart + "..." + endPart
        case .tail:
            let endIndex = text.index(text.startIndex, offsetBy: maxLength)
            return String(text[..<endIndex]) + "..."
        case .word:
            let endIndex = text.index(text.startIndex, offsetBy: maxLength)
            let truncated = String(text[..<endIndex])
            if let lastSpace = truncated.lastIndex(of: " ") {
                return String(truncated[..<lastSpace]) + "..."
            } else {
                return truncated + "..."
            }
        case .character:
            let endIndex = text.index(text.startIndex, offsetBy: maxLength)
            return String(text[..<endIndex]) + "..."
        }
    }
    
    /// Checks if text contains primarily RTL characters
    static func isRightToLeft(_ text: String) -> Bool {
        let rtlCharacterSet = CharacterSet(charactersIn: "\u{0590}-\u{05FF}\u{0600}-\u{06FF}\u{0750}-\u{077F}")
        return text.unicodeScalars.contains { rtlCharacterSet.contains($0) }
    }
    
    /// Returns appropriate text alignment for the given text
    static func optimalAlignment(for text: String) -> TextAlignment {
        return isRightToLeft(text) ? .trailing : .leading
    }
}

// MARK: - View Extensions

extension Text {
    /// Applies smart text processing based on content type
    func processedText(_ contentType: TextProcessing.ContentType) -> some View {
        let config = contentType.configuration
        
        return self
            .lineLimit(config.maxLines)
            .truncationMode(config.truncation.truncationMode)
            .minimumScaleFactor(config.minimumScaleFactor)
            .fixedSize(horizontal: false, vertical: config.useFixedSize)
            .textSelection(config.allowSelection ? .enabled : .disabled)
    }
    
    /// Applies smart text processing with custom configuration
    func processedText(configuration: TextProcessing.TextConfiguration) -> some View {
        return self
            .lineLimit(configuration.maxLines)
            .truncationMode(configuration.truncation.truncationMode)
            .minimumScaleFactor(configuration.minimumScaleFactor)
            .fixedSize(horizontal: false, vertical: configuration.useFixedSize)
            .textSelection(configuration.allowSelection ? .enabled : .disabled)
    }
}

extension View {
    /// Applies optimal text alignment based on content
    func smartTextAlignment(for text: String) -> some View {
        let alignment = TextProcessing.optimalAlignment(for: text)
        return self.multilineTextAlignment(alignment)
    }
}
