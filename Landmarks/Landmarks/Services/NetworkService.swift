/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Service for handling network operations with retry logic, caching, and error handling.
*/

import Foundation
import Network
import SwiftUI

/// Service for handling network operations
final class NetworkService: NetworkServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    
    @Published var isConnected: Bool = true
    
    private let session: URLSession
    private let monitor: NWPathMonitor
    private let monitorQueue = DispatchQueue(label: "networkservice.monitor")
    private var activeRequests: Set<URLSessionTask> = []
    private let requestQueue = DispatchQueue(label: "networkservice.requests", attributes: .concurrent)
    
    // Configuration
    private let maxRetryAttempts: Int = 3
    private let retryDelay: TimeInterval = 1.0
    private let timeoutInterval: TimeInterval = 30.0
    
    // MARK: - Initialization
    
    init(configuration: URLSessionConfiguration = .default) {
        // Configure session
        configuration.timeoutIntervalForRequest = timeoutInterval
        configuration.timeoutIntervalForResource = timeoutInterval * 2
        configuration.waitsForConnectivity = true
        
        self.session = URLSession(configuration: configuration)
        self.monitor = NWPathMonitor()
        
        setupNetworkMonitoring()
    }
    
    deinit {
        monitor.cancel()
        cancelAllRequests()
    }
    
    // MARK: - NetworkServiceProtocol
    
    func request<T: Decodable>(_ endpoint: NetworkEndpoint, responseType: T.Type) async throws -> T {
        guard isConnected else {
            throw NetworkServiceError.noConnection
        }
        
        var request = URLRequest(url: endpoint.url)
        request.httpMethod = endpoint.method.rawValue
        request.httpBody = endpoint.body
        
        // Set headers
        if let headers = endpoint.headers {
            for (key, value) in headers {
                request.setValue(value, forHTTPHeaderField: key)
            }
        }
        
        // Set default headers if not provided
        if request.value(forHTTPHeaderField: "Content-Type") == nil {
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        }
        
        return try await performRequestWithRetry(request: request, responseType: responseType)
    }
    
    func downloadData(from url: URL) async throws -> Data {
        guard isConnected else {
            throw NetworkServiceError.noConnection
        }
        
        let request = URLRequest(url: url)
        return try await performDataRequestWithRetry(request: request)
    }
    
    func uploadData(_ data: Data, to url: URL) async throws -> Data {
        guard isConnected else {
            throw NetworkServiceError.noConnection
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.httpBody = data
        request.setValue("application/octet-stream", forHTTPHeaderField: "Content-Type")
        
        return try await performDataRequestWithRetry(request: request)
    }
    
    func cancelAllRequests() {
        requestQueue.async(flags: .barrier) {
            for task in self.activeRequests {
                task.cancel()
            }
            self.activeRequests.removeAll()
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
            }
        }
        monitor.start(queue: monitorQueue)
    }
    
    private func performRequestWithRetry<T: Decodable>(
        request: URLRequest,
        responseType: T.Type,
        attemptCount: Int = 0
    ) async throws -> T {
        do {
            let data = try await performDataRequestWithRetry(request: request, attemptCount: attemptCount)
            return try JSONDecoder().decode(responseType, from: data)
        } catch NetworkServiceError.decodingError {
            // Don't retry decoding errors
            throw NetworkServiceError.decodingError
        } catch {
            if attemptCount < maxRetryAttempts {
                try await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
                return try await performRequestWithRetry(
                    request: request,
                    responseType: responseType,
                    attemptCount: attemptCount + 1
                )
            } else {
                throw error
            }
        }
    }
    
    private func performDataRequestWithRetry(
        request: URLRequest,
        attemptCount: Int = 0
    ) async throws -> Data {
        do {
            return try await performDataRequest(request: request)
        } catch {
            if attemptCount < maxRetryAttempts && shouldRetry(error: error) {
                try await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
                return try await performDataRequestWithRetry(
                    request: request,
                    attemptCount: attemptCount + 1
                )
            } else {
                throw error
            }
        }
    }
    
    private func performDataRequest(request: URLRequest) async throws -> Data {
        let task = session.dataTask(with: request) { _, _, _ in }
        
        // Track active request
        requestQueue.async(flags: .barrier) {
            self.activeRequests.insert(task)
        }
        
        defer {
            requestQueue.async(flags: .barrier) {
                self.activeRequests.remove(task)
            }
        }
        
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkServiceError.invalidResponse
            }
            
            try validateResponse(httpResponse)
            
            return data
        } catch {
            throw mapError(error)
        }
    }
    
    private func validateResponse(_ response: HTTPURLResponse) throws {
        switch response.statusCode {
        case 200...299:
            return
        case 400...499:
            throw NetworkServiceError.clientError(response.statusCode)
        case 500...599:
            throw NetworkServiceError.serverError(response.statusCode)
        default:
            throw NetworkServiceError.unexpectedStatusCode(response.statusCode)
        }
    }
    
    private func shouldRetry(error: Error) -> Bool {
        switch error {
        case NetworkServiceError.noConnection,
             NetworkServiceError.timeout,
             NetworkServiceError.serverError:
            return true
        case NetworkServiceError.clientError,
             NetworkServiceError.decodingError,
             NetworkServiceError.invalidResponse:
            return false
        default:
            return true
        }
    }
    
    private func mapError(_ error: Error) -> NetworkServiceError {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return .noConnection
            case .timedOut:
                return .timeout
            case .cannotDecodeRawData, .cannotDecodeContentData:
                return .decodingError
            default:
                return .requestFailed(error)
            }
        }
        
        return .requestFailed(error)
    }
}

// MARK: - Network Service Errors

enum NetworkServiceError: Error, LocalizedError {
    case noConnection
    case timeout
    case invalidResponse
    case clientError(Int)
    case serverError(Int)
    case unexpectedStatusCode(Int)
    case decodingError
    case requestFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .noConnection:
            return "No internet connection available"
        case .timeout:
            return "Request timed out"
        case .invalidResponse:
            return "Invalid response received"
        case .clientError(let code):
            return "Client error with status code \(code)"
        case .serverError(let code):
            return "Server error with status code \(code)"
        case .unexpectedStatusCode(let code):
            return "Unexpected status code \(code)"
        case .decodingError:
            return "Failed to decode response data"
        case .requestFailed(let error):
            return "Request failed: \(error.localizedDescription)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .noConnection:
            return "Please check your internet connection and try again"
        case .timeout:
            return "Please try again later"
        case .clientError, .serverError:
            return "Please try again later or contact support"
        default:
            return "Please try again"
        }
    }
}