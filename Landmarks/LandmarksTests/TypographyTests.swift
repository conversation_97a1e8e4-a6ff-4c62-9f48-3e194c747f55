/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Unit tests for the Typography system to ensure consistent text styling and behavior.
*/

import XCTest
import SwiftUI
@testable import Landmarks

final class TypographyTests: XCTestCase {
    
    // MARK: - Typography Style Tests
    
    func testTypographyStyleFonts() {
        // Test that each typography style returns the expected font
        XCTAssertEqual(Typography.TextStyle.heroTitle.font, .largeTitle)
        XCTAssertEqual(Typography.TextStyle.title.font, .title2)
        XCTAssertEqual(Typography.TextStyle.subtitle.font, .title3)
        XCTAssertEqual(Typography.TextStyle.body.font, .body)
        XCTAssertEqual(Typography.TextStyle.bodyEmphasized.font, .body)
        XCTAssertEqual(Typography.TextStyle.caption.font, .caption)
        XCTAssertEqual(Typography.TextStyle.badge.font, .callout)
        XCTAssertEqual(Typography.TextStyle.button.font, .body)
    }
    
    func testTypographyStyleWeights() {
        // Test that each typography style returns the expected font weight
        XCTAssertEqual(Typography.TextStyle.heroTitle.weight, .bold)
        XCTAssertEqual(Typography.TextStyle.title.weight, .semibold)
        XCTAssertEqual(Typography.TextStyle.subtitle.weight, .semibold)
        XCTAssertEqual(Typography.TextStyle.body.weight, .regular)
        XCTAssertEqual(Typography.TextStyle.bodyEmphasized.weight, .medium)
        XCTAssertEqual(Typography.TextStyle.caption.weight, .regular)
        XCTAssertEqual(Typography.TextStyle.badge.weight, .medium)
        XCTAssertEqual(Typography.TextStyle.button.weight, .medium)
    }
    
    func testTypographyStyleLineSpacing() {
        // Test that line spacing values are within expected ranges
        XCTAssertGreaterThan(Typography.TextStyle.heroTitle.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.title.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.subtitle.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.body.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.bodyEmphasized.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.caption.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.badge.lineSpacing, 0)
        XCTAssertGreaterThan(Typography.TextStyle.button.lineSpacing, 0)
    }
    
    // MARK: - Color System Tests
    
    func testSemanticColors() {
        // Test that semantic colors are properly defined
        XCTAssertNotNil(Typography.TextColor.primary.color)
        XCTAssertNotNil(Typography.TextColor.secondary.color)
        XCTAssertNotNil(Typography.TextColor.tertiary.color)
        XCTAssertNotNil(Typography.TextColor.onImage.color)
        XCTAssertNotNil(Typography.TextColor.onImageSecondary.color)
        XCTAssertNotNil(Typography.TextColor.accent.color)
        XCTAssertNotNil(Typography.TextColor.error.color)
        XCTAssertNotNil(Typography.TextColor.success.color)
        XCTAssertNotNil(Typography.TextColor.warning.color)
    }
    
    // MARK: - Responsive Design Tests
    
    @MainActor
    func testResponsiveTypographyScaling() {
        // Test that responsive scaling factors are reasonable
        let scale = ResponsiveDesign.TypographyScale.current
        
        XCTAssertGreaterThan(scale.heroTitle, 0.5)
        XCTAssertLessThan(scale.heroTitle, 2.0)
        
        XCTAssertGreaterThan(scale.title, 0.5)
        XCTAssertLessThan(scale.title, 2.0)
        
        XCTAssertGreaterThan(scale.subtitle, 0.5)
        XCTAssertLessThan(scale.subtitle, 2.0)
        
        XCTAssertGreaterThan(scale.body, 0.5)
        XCTAssertLessThan(scale.body, 2.0)
        
        XCTAssertGreaterThan(scale.caption, 0.5)
        XCTAssertLessThan(scale.caption, 2.0)
    }
    
    @MainActor
    func testResponsiveSpacing() {
        // Test that responsive spacing values are reasonable
        let spacing = ResponsiveDesign.SpacingScale.current
        
        XCTAssertGreaterThan(spacing.small, 0)
        XCTAssertGreaterThan(spacing.medium, spacing.small)
        XCTAssertGreaterThan(spacing.large, spacing.medium)
        XCTAssertGreaterThan(spacing.extraLarge, spacing.large)
    }
    
    @MainActor
    func testResponsiveLayoutAdaptation() {
        // Test that layout adaptation values are reasonable
        let layout = ResponsiveDesign.LayoutAdaptation.current
        
        XCTAssertGreaterThan(layout.contentInset, 0)
        XCTAssertGreaterThan(layout.gridColumns, 0)
        XCTAssertLessThan(layout.gridColumns, 10) // Reasonable upper bound
        XCTAssertGreaterThan(layout.gridSpacing, 0)
        XCTAssertGreaterThan(layout.cardCornerRadius, 0)
    }
    
    // MARK: - Text Processing Tests
    
    func testTextTruncation() {
        let longText = "This is a very long text that should be truncated when it exceeds the maximum length limit."
        let maxLength = 20
        
        let truncatedTail = TextProcessing.smartTruncate(longText, to: maxLength, strategy: .tail)
        XCTAssertLessThanOrEqual(truncatedTail.count, maxLength + 3) // +3 for "..."
        XCTAssertTrue(truncatedTail.hasSuffix("..."))
        
        let truncatedWord = TextProcessing.smartTruncate(longText, to: maxLength, strategy: .word)
        XCTAssertLessThanOrEqual(truncatedWord.count, maxLength + 3)
        XCTAssertTrue(truncatedWord.hasSuffix("..."))
        
        let truncatedMiddle = TextProcessing.smartTruncate(longText, to: maxLength, strategy: .middle)
        XCTAssertLessThanOrEqual(truncatedMiddle.count, maxLength + 3)
        XCTAssertTrue(truncatedMiddle.contains("..."))
    }
    
    func testTextAlignment() {
        let englishText = "This is English text"
        let arabicText = "هذا نص عربي"
        let hebrewText = "זה טקסט עברי"
        
        XCTAssertEqual(TextProcessing.optimalAlignment(for: englishText), .leading)
        XCTAssertEqual(TextProcessing.optimalAlignment(for: arabicText), .trailing)
        XCTAssertEqual(TextProcessing.optimalAlignment(for: hebrewText), .trailing)
    }
    
    func testRTLDetection() {
        let englishText = "This is English text"
        let arabicText = "هذا نص عربي"
        let hebrewText = "זה טקסט עברי"
        let mixedText = "English and عربي mixed"
        
        XCTAssertFalse(TextProcessing.isRightToLeft(englishText))
        XCTAssertTrue(TextProcessing.isRightToLeft(arabicText))
        XCTAssertTrue(TextProcessing.isRightToLeft(hebrewText))
        XCTAssertTrue(TextProcessing.isRightToLeft(mixedText))
    }
    
    func testContentTypeConfigurations() {
        // Test that content type configurations are properly defined
        let titleConfig = TextProcessing.ContentType.title.configuration
        XCTAssertNotNil(titleConfig.maxLines)
        XCTAssertEqual(titleConfig.maxLines, 3)
        XCTAssertFalse(titleConfig.allowSelection)
        
        let bodyConfig = TextProcessing.ContentType.body.configuration
        XCTAssertNil(bodyConfig.maxLines)
        XCTAssertTrue(bodyConfig.allowSelection)
        
        let captionConfig = TextProcessing.ContentType.caption.configuration
        XCTAssertNotNil(captionConfig.maxLines)
        XCTAssertEqual(captionConfig.maxLines, 2)
        XCTAssertFalse(captionConfig.allowSelection)
    }
    
    // MARK: - Badge Tests
    
    func testBadgeFontSizes() {
        // Test that badge font sizes are reasonable
        for badge in Badge.allCases {
            let normalSize = badge.fontSize(forEarnedView: false)
            let earnedSize = badge.fontSize(forEarnedView: true)
            
            XCTAssertGreaterThan(normalSize, 10)
            XCTAssertLessThan(normalSize, 50)
            XCTAssertGreaterThan(earnedSize, normalSize)
            XCTAssertLessThan(earnedSize, 60)
        }
    }
    
    func testBadgeColors() {
        // Test that all badges have defined colors
        for badge in Badge.allCases {
            XCTAssertNotNil(badge.badgeColor)
        }
    }
    
    // MARK: - Constants Tests

    func testTypographyConstants() {
        // Test that typography constants are reasonable
        XCTAssertGreaterThan(Constants.defaultLineSpacing, 0)
        XCTAssertGreaterThan(Constants.compactLineSpacing, 0)
        XCTAssertGreaterThan(Constants.relaxedLineSpacing, Constants.defaultLineSpacing)

        XCTAssertGreaterThan(Constants.textShadowRadius, 0)
        XCTAssertLessThan(Constants.textShadowRadius, 10)

        XCTAssertGreaterThan(Constants.smallDeviceFontScale, 0.5)
        XCTAssertLessThan(Constants.smallDeviceFontScale, 1.5)

        XCTAssertGreaterThan(Constants.largeDeviceFontScale, 0.5)
        XCTAssertLessThan(Constants.largeDeviceFontScale, 2.0)
    }

    // MARK: - Performance Tests

    func testTypographyPerformance() {
        measure {
            // Test performance of typography style calculations
            for _ in 0..<1000 {
                let _ = Typography.TextStyle.heroTitle.font
                let _ = Typography.TextStyle.heroTitle.weight
                let _ = Typography.TextStyle.heroTitle.lineSpacing
            }
        }
    }

    @MainActor
    func testResponsiveDesignPerformance() {
        measure {
            // Test performance of responsive design calculations
            for _ in 0..<100 {
                let _ = ResponsiveDesign.TypographyScale.current
                let _ = ResponsiveDesign.SpacingScale.current
                let _ = ResponsiveDesign.LayoutAdaptation.current
            }
        }
    }

    func testTextProcessingPerformance() {
        let longText = String(repeating: "This is a test sentence. ", count: 100)

        measure {
            // Test performance of text processing operations
            for _ in 0..<100 {
                let _ = TextProcessing.smartTruncate(longText, to: 50, strategy: .word)
                let _ = TextProcessing.isRightToLeft(longText)
                let _ = TextProcessing.optimalAlignment(for: longText)
            }
        }
    }

    // MARK: - Integration Tests

    func testTypographySystemIntegration() {
        // Test that all components work together
        let textStyle = Typography.TextStyle.body
        let textColor = Typography.TextColor.primary
        let contentType = TextProcessing.ContentType.body

        XCTAssertNotNil(textStyle.font)
        XCTAssertNotNil(textColor.color)
        XCTAssertNotNil(contentType.configuration)

        // Test that configurations are compatible
        let config = contentType.configuration
        XCTAssertTrue(config.allowSelection) // Body text should be selectable
        XCTAssertNil(config.maxLines) // Body text should not have line limits
    }
}
