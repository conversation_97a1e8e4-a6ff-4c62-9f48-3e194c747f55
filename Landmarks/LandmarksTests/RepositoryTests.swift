/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Unit tests for Repository pattern implementation.
*/

import XCTest
import Combine
import MapKit
import CoreLocation
@testable import Landmarks

final class RepositoryTests: XCTestCase {
    
    var landmarkRepository: LandmarkRepository!
    var collectionRepository: CollectionRepository!
    var mockMapService: MockMapService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUpWithError() throws {
        mockMapService = MockMapService()
        landmarkRepository = LandmarkRepository(mapService: mockMapService)
        collectionRepository = CollectionRepository(landmarkRepository: landmarkRepository)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDownWithError() throws {
        landmarkRepository = nil
        collectionRepository = nil
        mockMapService = nil
        cancellables = nil
    }
    
    // MARK: - LandmarkRepository Tests
    
    func testFetchAllLandmarks() async throws {
        // When
        let landmarks = try await landmarkRepository.fetchAll()
        
        // Then
        XCTAssertFalse(landmarks.isEmpty, "Should fetch landmarks")
        XCTAssertEqual(landmarks.count, Landmark.exampleData.count, "Should fetch all example landmarks")
    }
    
    func testFetchLandmarkById() async throws {
        // Given
        _ = try await landmarkRepository.fetchAll()
        
        // When
        let landmark = try await landmarkRepository.fetch(by: 1016)
        
        // Then
        XCTAssertNotNil(landmark, "Should find landmark with ID 1016")
        XCTAssertEqual(landmark?.id, 1016, "Should return correct landmark")
    }
    
    func testFetchNonExistentLandmark() async throws {
        // Given
        _ = try await landmarkRepository.fetchAll()
        
        // When
        let landmark = try await landmarkRepository.fetch(by: 99999)
        
        // Then
        XCTAssertNil(landmark, "Should return nil for non-existent landmark")
    }
    
    func testSearchLandmarks() async throws {
        // Given
        _ = try await landmarkRepository.fetchAll()
        
        // When
        let results = try await landmarkRepository.search(query: "mount")
        
        // Then
        XCTAssertFalse(results.isEmpty, "Should find landmarks with 'mount' in name or description")
        
        // Verify all results contain the search term
        for landmark in results {
            let name = String(localized: landmark.name).lowercased()
            let description = String(localized: landmark.description).lowercased()
            let continent = landmark.continent.lowercased()
            
            XCTAssertTrue(
                name.contains("mount") || description.contains("mount") || continent.contains("mount"),
                "Search result should contain the search term"
            )
        }
    }
    
    func testSearchEmptyQuery() async throws {
        // Given
        let allLandmarks = try await landmarkRepository.fetchAll()
        
        // When
        let results = try await landmarkRepository.search(query: "")
        
        // Then
        XCTAssertEqual(results.count, allLandmarks.count, "Empty query should return all landmarks")
    }
    
    func testGetFeaturedLandmark() async throws {
        // Given
        _ = try await landmarkRepository.fetchAll()
        
        // When
        let featured = try await landmarkRepository.getFeaturedLandmark()
        
        // Then
        XCTAssertNotNil(featured, "Should have a featured landmark")
        XCTAssertEqual(featured?.id, 1016, "Featured landmark should be Mount Fuji (ID: 1016)")
    }
    
    func testGetLandmarksByContinent() async throws {
        // Given
        _ = try await landmarkRepository.fetchAll()
        
        // When
        let asiaLandmarks = try await landmarkRepository.getLandmarks(for: .asia)
        
        // Then
        XCTAssertFalse(asiaLandmarks.isEmpty, "Should have landmarks in Asia")
        
        // Verify all landmarks are from Asia
        for landmark in asiaLandmarks {
            XCTAssertEqual(landmark.continent, "Asia", "All landmarks should be from Asia")
        }
        
        // Verify they are sorted by name
        let sortedNames = asiaLandmarks.map { String(localized: $0.name) }
        let expectedSortedNames = sortedNames.sorted()
        XCTAssertEqual(sortedNames, expectedSortedNames, "Landmarks should be sorted by name")
    }
    
    func testCachingBehavior() async throws {
        // Given
        let config = RepositoryConfiguration(
            cacheEnabled: true,
            cacheTTL: 300,
            enableOfflineMode: true,
            syncWithRemote: false
        )
        let cachedRepository = LandmarkRepository(mapService: mockMapService, configuration: config)
        
        // When - First fetch
        let landmarks1 = try await cachedRepository.fetchAll()
        
        // When - Second fetch (should use cache)
        let landmarks2 = try await cachedRepository.fetchAll()
        
        // Then
        XCTAssertEqual(landmarks1.count, landmarks2.count, "Cached results should match")
    }
    
    // MARK: - CollectionRepository Tests
    
    func testFetchAllCollections() async throws {
        // When
        let collections = try await collectionRepository.fetchAll()
        
        // Then
        XCTAssertFalse(collections.isEmpty, "Should fetch collections")
        XCTAssertEqual(collections.count, LandmarkCollection.exampleData.count, "Should fetch all example collections")
    }
    
    func testGetFavoritesCollection() async throws {
        // When
        let favorites = try await collectionRepository.getFavoritesCollection()
        
        // Then
        XCTAssertEqual(favorites.id, 1001, "Favorites collection should have ID 1001")
    }
    
    func testGetUserCollections() async throws {
        // When
        let userCollections = try await collectionRepository.getUserCollections()
        
        // Then
        // Should exclude favorites collection (ID: 1001)
        for collection in userCollections {
            XCTAssertNotEqual(collection.id, 1001, "User collections should not include favorites")
        }
    }
    
    func testCreateNewUserCollection() async throws {
        // When
        let newCollection = try await collectionRepository.createNewUserCollection()
        
        // Then
        XCTAssertNotNil(newCollection, "Should create new collection")
        XCTAssertNotEqual(newCollection.id, 1001, "New collection should not have favorites ID")
        XCTAssertEqual(String(localized: newCollection.name), "New Collection", "Should have default name")
    }
    
    func testAddLandmarkToCollection() async throws {
        // Given
        let collections = try await collectionRepository.fetchAll()
        guard let testCollection = collections.first(where: { $0.id != 1001 }) else {
            XCTFail("Need a non-favorites collection for testing")
            return
        }
        
        let landmarks = try await landmarkRepository.fetchAll()
        guard let testLandmark = landmarks.first else {
            XCTFail("Need a landmark for testing")
            return
        }
        
        let initialCount = testCollection.landmarks.count
        
        // When
        let updatedCollection = try await collectionRepository.addLandmark(testLandmark, to: testCollection)
        
        // Then
        XCTAssertEqual(updatedCollection.landmarks.count, initialCount + 1, "Should add landmark to collection")
        XCTAssertTrue(updatedCollection.landmarks.contains(testLandmark), "Collection should contain the added landmark")
    }
    
    func testToggleFavorite() async throws {
        // Given
        let landmarks = try await landmarkRepository.fetchAll()
        guard let testLandmark = landmarks.first else {
            XCTFail("Need a landmark for testing")
            return
        }
        
        let initialFavoriteStatus = try await collectionRepository.isFavorite(testLandmark)
        
        // When
        try await collectionRepository.toggleFavorite(testLandmark)
        
        // Then
        let newFavoriteStatus = try await collectionRepository.isFavorite(testLandmark)
        XCTAssertNotEqual(initialFavoriteStatus, newFavoriteStatus, "Favorite status should toggle")
    }
    
    func testCannotDeleteFavoritesCollection() async throws {
        // Given
        let favoritesId = 1001
        
        // When & Then
        do {
            try await collectionRepository.delete(by: favoritesId)
            XCTFail("Should not be able to delete favorites collection")
        } catch CollectionRepositoryError.cannotDeleteFavorites {
            // Expected error
        } catch {
            XCTFail("Should throw cannotDeleteFavorites error, got: \(error)")
        }
    }
}

// MARK: - Mock MapService

class MockMapService: MapServiceProtocol {
    func fetchMapItems(for landmarks: [Landmark]) async throws -> [Int: MKMapItem] {
        // Return empty dictionary for testing
        return [:]
    }
    
    func fetchMapItem(for landmark: Landmark) async throws -> MKMapItem? {
        // Return nil for testing
        return nil
    }
    
    func searchPlaces(near coordinate: CLLocationCoordinate2D, radius: CLLocationDistance) async throws -> [MKMapItem] {
        return []
    }
    
    func getDirections(from source: MKMapItem, to destination: MKMapItem) async throws -> MKDirections.Response {
        throw MapServiceError.networkUnavailable
    }
}