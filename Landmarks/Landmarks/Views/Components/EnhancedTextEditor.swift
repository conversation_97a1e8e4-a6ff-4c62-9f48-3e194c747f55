/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
An enhanced text editor with improved long text handling, accessibility, and user experience.
*/

import SwiftUI

/// An enhanced text editor that provides better handling of long text, accessibility features, and improved UX.
struct EnhancedTextEditor: View {
    @Binding var text: String
    let placeholder: String
    let minHeight: CGFloat
    let maxHeight: CGFloat?
    let contentType: TextProcessing.ContentType
    
    @State private var isEditing = false
    @State private var textHeight: CGFloat = 0
    
    init(
        text: Binding<String>,
        placeholder: String = "Enter text...",
        minHeight: CGFloat = 88,
        maxHeight: CGFloat? = nil,
        contentType: TextProcessing.ContentType = .body
    ) {
        self._text = text
        self.placeholder = placeholder
        self.minHeight = minHeight
        self.maxHeight = maxHeight
        self.contentType = contentType
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            ZStack(alignment: .topLeading) {
                // Background
                RoundedRectangle(cornerRadius: Constants.cornerRadius)
                    .fill(ColorSystem.SemanticColor.backgroundSecondary.color)
                    .overlay(
                        RoundedRectangle(cornerRadius: Constants.cornerRadius)
                            .stroke(
                                isEditing ? 
                                ColorSystem.SemanticColor.textAccent.color :
                                ColorSystem.SemanticColor.borderSecondary.color,
                                lineWidth: isEditing ? 2 : 1
                            )
                    )
                
                // Placeholder text
                if text.isEmpty && !isEditing {
                    Text(placeholder)
                        .responsiveTypography(.body, color: .tertiary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .allowsHitTesting(false)
                }
                
                // Text editor
                TextEditor(text: $text)
                    .responsiveTypography(.body)
                    .scrollContentBackground(.hidden)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .frame(minHeight: minHeight)
                    .frame(maxHeight: maxHeight ?? .infinity)
                    .onTapGesture {
                        isEditing = true
                    }
                    .onSubmit {
                        isEditing = false
                    }
                    .onChange(of: text) { _, newValue in
                        // Auto-resize based on content
                        updateTextHeight(for: newValue)
                    }
            }
            
            // Character count and status
            if isEditing || !text.isEmpty {
                HStack {
                    if !text.isEmpty {
                        Text("\(text.count) characters")
                            .responsiveTypography(.caption, color: .secondary)
                    }
                    
                    Spacer()
                    
                    if isEditing {
                        Button("Done") {
                            isEditing = false
                            hideKeyboard()
                        }
                        .responsiveTypography(.caption, color: .accent)
                    }
                }
                .padding(.horizontal, 4)
                .padding(.top, 4)
                .animation(.easeInOut(duration: 0.2), value: isEditing)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Text editor")
        .accessibilityHint("Double tap to edit text")
    }
    
    private func updateTextHeight(for text: String) {
        // This would calculate the optimal height based on text content
        // For now, we'll use a simple estimation
        let lineCount = max(1, text.components(separatedBy: .newlines).count)
        let estimatedHeight = CGFloat(lineCount) * 20 + 24 // 20pt per line + padding
        textHeight = max(minHeight, min(maxHeight ?? .infinity, estimatedHeight))
    }
    
    private func hideKeyboard() {
        #if os(iOS)
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        #endif
    }
}

/// A read-only text view optimized for displaying long content
struct EnhancedTextView: View {
    let text: String
    let contentType: TextProcessing.ContentType
    let isExpandable: Bool
    
    @State private var isExpanded = false
    @State private var isTruncated = false
    
    private let previewLineLimit = 3
    
    init(
        text: String,
        contentType: TextProcessing.ContentType = .body,
        isExpandable: Bool = true
    ) {
        self.text = text
        self.contentType = contentType
        self.isExpandable = isExpandable
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: Constants.compactLineSpacing) {
            Text(text)
                .responsiveTypography(.body)
                .processedText(contentType)
                .smartTextAlignment(for: text)
                .lineLimit(isExpanded ? nil : (isExpandable ? previewLineLimit : nil))
                .background(
                    // Hidden text to measure if truncation is needed
                    Text(text)
                        .responsiveTypography(.body)
                        .lineLimit(previewLineLimit)
                        .background(GeometryReader { geometry in
                            Color.clear.onAppear {
                                checkIfTruncated(geometry: geometry)
                            }
                        })
                        .hidden()
                )
            
            if isExpandable && isTruncated {
                Button(isExpanded ? "Show Less" : "Show More") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }
                .responsiveTypography(.caption, color: .accent)
                .padding(.top, Constants.compactLineSpacing)
            }
        }
    }
    
    private func checkIfTruncated(geometry: GeometryProxy) {
        // This is a simplified check - in a real implementation,
        // you'd compare the full text height with the truncated height
        let estimatedLines = TextProcessing.estimateLineCount(
            for: text,
            width: geometry.size.width,
            font: .body
        )
        isTruncated = estimatedLines > previewLineLimit
    }
}

/// A smart text container that automatically chooses between editor and view modes
struct SmartTextContainer: View {
    @Binding var text: String
    let isEditable: Bool
    let placeholder: String
    let contentType: TextProcessing.ContentType
    
    init(
        text: Binding<String>,
        isEditable: Bool = true,
        placeholder: String = "Enter text...",
        contentType: TextProcessing.ContentType = .body
    ) {
        self._text = text
        self.isEditable = isEditable
        self.placeholder = placeholder
        self.contentType = contentType
    }
    
    var body: some View {
        if isEditable {
            EnhancedTextEditor(
                text: $text,
                placeholder: placeholder,
                contentType: contentType
            )
        } else {
            EnhancedTextView(
                text: text,
                contentType: contentType
            )
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        EnhancedTextEditor(
            text: .constant("This is a sample text in the enhanced text editor."),
            placeholder: "Enter your description..."
        )
        
        EnhancedTextView(
            text: "This is a very long text that demonstrates the expandable text view functionality. It will show a 'Show More' button when the text exceeds the preview line limit. This allows users to see a preview of the content and expand it when they want to read the full text. The text view also supports smart text alignment based on the content's language direction."
        )
    }
    .padding()
}
