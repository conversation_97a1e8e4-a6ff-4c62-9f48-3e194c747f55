#!/bin/bash

echo "Checking Swift syntax for core files..."

# Check core architecture files
echo "Checking Repository files..."
find Landmarks/Landmarks/Repository -name "*.swift" | while read file; do
    echo "Checking $file"
    if ! swift -frontend -parse-as-library "$file" >/dev/null 2>&1; then
        echo "❌ Syntax error in $file"
        swift -frontend -parse-as-library "$file"
    else
        echo "✅ $file is valid"
    fi
done

echo "Checking Service files..."
find Landmarks/Landmarks/Services -name "*.swift" | while read file; do
    echo "Checking $file"
    if ! swift -frontend -parse-as-library "$file" >/dev/null 2>&1; then
        echo "❌ Syntax error in $file"
        swift -frontend -parse-as-library "$file"
    else
        echo "✅ $file is valid"
    fi
done

echo "Checking Core files..."
find Landmarks/Landmarks/Core -name "*.swift" | while read file; do
    echo "Checking $file"
    if ! swift -frontend -parse-as-library "$file" >/dev/null 2>&1; then
        echo "❌ Syntax error in $file"
        swift -frontend -parse-as-library "$file"
    else
        echo "✅ $file is valid"
    fi
done