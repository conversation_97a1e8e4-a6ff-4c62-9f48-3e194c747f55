/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
A view that shows the featured landmark with a large image.
*/

import SwiftUI

/// A view that shows the featured landmark with a large image.
struct LandmarkFeaturedItemView: View {
    @Environment(ModelData.self) var modelData
    let landmark: Landmark

    var body: some View {
        NavigationLink(value: landmark) {
            Image(decorative: landmark.backgroundImageName)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity)
                .clipped()
                .backgroundExtensionEffect()
                .overlay {
                    EnhancedReadabilityOverlay(intensity: 0.7, gradientHeight: 0.7)
                }
                .overlay(alignment: .bottom) {
                    VStack(spacing: Constants.standardPadding) {
                        Text("Featured Landmark", comment: "Big headline in the main image of featured landmarks.")
                            .responsiveTypographyOnImage(.subtitle, color: .onImageSecondary)
                            .processedText(.subtitle)
                            .multilineTextAlignment(.center)

                        Text(landmark.name)
                            .responsiveTypographyOnImage(.heroTitle)
                            .processedText(.title)
                            .smartTextAlignment(for: landmark.name)

                        But<PERSON>("Learn More") {
                            modelData.path.append(landmark)
                        }
                        .buttonStyle(.borderedProminent)
                        .padding(.bottom, Constants.learnMorePadding)
                    }
                    .padding([.bottom], Constants.learnMoreBottomPadding)
                    .responsiveContentInsets()
                }
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    let modelData = ModelData()
    let previewLandmark = modelData.landmarksById[1012] ?? modelData.landmarks.first!

    LandmarkFeaturedItemView(landmark: previewLandmark)
        .frame(height: 400.0)
        .environment(modelData)
}
