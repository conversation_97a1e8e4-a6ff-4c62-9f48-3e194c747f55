#!/usr/bin/env python3

import os
import subprocess
import sys

def check_file_syntax(file_path):
    """Check if a Swift file has basic syntax errors"""
    try:
        # Use swiftc -parse to check syntax
        result = subprocess.run(['swiftc', '-parse', file_path], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {file_path} - OK")
            return True
        else:
            print(f"❌ {file_path} - ERROR:")
            print(result.stderr)
            return False
    except FileNotFoundError:
        print(f"⚠️  Swift compiler not available, skipping {file_path}")
        return True
    except Exception as e:
        print(f"⚠️  Error checking {file_path}: {e}")
        return True

def main():
    # Key files to check
    key_files = [
        "Landmarks/Landmarks/Core/AppError.swift",
        "Landmarks/Landmarks/Core/ErrorHandler.swift", 
        "Landmarks/Landmarks/Core/AppModelData.swift",
        "Landmarks/Landmarks/Repository/RepositoryProtocol.swift",
        "Landmarks/Landmarks/Repository/BaseRepository.swift",
        "Landmarks/Landmarks/Repository/LandmarkRepository.swift",
        "Landmarks/Landmarks/Services/NetworkService.swift",
        "Landmarks/Landmarks/Services/MapService.swift",
        "Landmarks/Landmarks/Cache/ImageCache.swift",
        "Landmarks/Landmarks/Model/ResponsiveDesignCache.swift"
    ]
    
    print("Checking syntax of key architecture files...")
    print("=" * 50)
    
    all_good = True
    for file_path in key_files:
        if os.path.exists(file_path):
            if not check_file_syntax(file_path):
                all_good = False
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("=" * 50)
    if all_good:
        print("✅ All checked files have valid syntax!")
    else:
        print("❌ Some files have syntax errors that need to be fixed.")
        sys.exit(1)

if __name__ == "__main__":
    main()