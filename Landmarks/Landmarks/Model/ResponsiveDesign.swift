/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
A responsive design system that adapts typography and layout to different device types and screen sizes.
*/

import SwiftUI
#if os(iOS)
import UIKit
#elseif os(macOS)
import AppKit
#endif

/// A responsive design system that provides device-specific adaptations for typography and layout.
struct ResponsiveDesign {
    
    // MARK: - Device Types
    
    /// Enumeration of supported device types
    enum DeviceType {
        case iPhone
        case iPadCompact
        case iPadRegular
        case mac
        
        /// Current device type based on the environment
        @MainActor
        static var current: DeviceType {
            #if os(iOS)
            if UIDevice.current.userInterfaceIdiom == .pad {
                // Check for compact size class to distinguish between split view and full screen
                return .iPadRegular // Default to regular, can be refined with size classes
            } else {
                return .iPhone
            }
            #else
            return .mac
            #endif
        }
    }
    
    // MARK: - Screen Size Categories
    
    /// Screen size categories for responsive design
    enum ScreenSize {
        case compact    // iPhone, iPad split view
        case regular    // iPad full screen
        case large      // Large iPad, Mac
        
        @MainActor
        static var current: ScreenSize {
            #if os(iOS)
            let screenWidth = UIScreen.main.bounds.width
            if UIDevice.current.userInterfaceIdiom == .pad {
                return screenWidth > 1000 ? .large : .regular
            } else {
                return .compact
            }
            #else
            return .large
            #endif
        }
    }
    
    // MARK: - Typography Scaling
    
    /// Typography scaling factors for different devices
    struct TypographyScale {
        let heroTitle: CGFloat
        let title: CGFloat
        let subtitle: CGFloat
        let body: CGFloat
        let caption: CGFloat
        
        /// Returns the appropriate typography scale for the current device
        @MainActor
        static var current: TypographyScale {
            switch DeviceType.current {
            case .iPhone:
                return TypographyScale(
                    heroTitle: 0.9,
                    title: 0.95,
                    subtitle: 0.95,
                    body: 1.0,
                    caption: 1.0
                )
            case .iPadCompact:
                return TypographyScale(
                    heroTitle: 1.0,
                    title: 1.0,
                    subtitle: 1.0,
                    body: 1.0,
                    caption: 1.0
                )
            case .iPadRegular:
                return TypographyScale(
                    heroTitle: 1.1,
                    title: 1.05,
                    subtitle: 1.05,
                    body: 1.0,
                    caption: 1.0
                )
            case .mac:
                return TypographyScale(
                    heroTitle: 1.2,
                    title: 1.1,
                    subtitle: 1.05,
                    body: 1.0,
                    caption: 1.0
                )
            }
        }
        
        /// Returns the scale factor for a given typography style
        func scale(for style: Typography.TextStyle) -> CGFloat {
            switch style {
            case .heroTitle:
                return heroTitle
            case .title:
                return title
            case .subtitle:
                return subtitle
            case .body, .bodyEmphasized:
                return body
            case .caption, .badge, .button:
                return caption
            }
        }
    }
    
    // MARK: - Spacing Scale
    
    /// Spacing scale for different devices
    struct SpacingScale {
        let small: CGFloat
        let medium: CGFloat
        let large: CGFloat
        let extraLarge: CGFloat
        
        @MainActor
        static var current: SpacingScale {
            switch DeviceType.current {
            case .iPhone:
                return SpacingScale(
                    small: 8,
                    medium: 16,
                    large: 24,
                    extraLarge: 32
                )
            case .iPadCompact:
                return SpacingScale(
                    small: 10,
                    medium: 18,
                    large: 28,
                    extraLarge: 36
                )
            case .iPadRegular:
                return SpacingScale(
                    small: 12,
                    medium: 20,
                    large: 32,
                    extraLarge: 44
                )
            case .mac:
                return SpacingScale(
                    small: 12,
                    medium: 20,
                    large: 32,
                    extraLarge: 48
                )
            }
        }
    }
    
    // MARK: - Layout Adaptations
    
    /// Layout adaptations for different screen sizes
    struct LayoutAdaptation {
        let contentInset: CGFloat
        let gridColumns: Int
        let gridSpacing: CGFloat
        let cardCornerRadius: CGFloat
        
        @MainActor
        static var current: LayoutAdaptation {
            switch ScreenSize.current {
            case .compact:
                return LayoutAdaptation(
                    contentInset: 16,
                    gridColumns: 2,
                    gridSpacing: 12,
                    cardCornerRadius: 12
                )
            case .regular:
                return LayoutAdaptation(
                    contentInset: 24,
                    gridColumns: 3,
                    gridSpacing: 16,
                    cardCornerRadius: 16
                )
            case .large:
                return LayoutAdaptation(
                    contentInset: 32,
                    gridColumns: 4,
                    gridSpacing: 20,
                    cardCornerRadius: 20
                )
            }
        }
    }
}

// MARK: - View Extensions

extension Text {
    /// Applies responsive typography scaling
    @MainActor
    func responsiveTypography(_ style: Typography.TextStyle, color: Typography.TextColor = .primary) -> some View {
        let scale = ResponsiveDesign.TypographyScale.current.scale(for: style)
        return self
            .typography(style, color: color)
            .scaleEffect(scale)
    }
    
    /// Applies responsive typography with image readability enhancements
    @MainActor
    func responsiveTypographyOnImage(_ style: Typography.TextStyle, color: Typography.TextColor = .onImage) -> some View {
        let scale = ResponsiveDesign.TypographyScale.current.scale(for: style)
        return self
            .typographyOnImageEnhanced(style, color: color)
            .scaleEffect(scale)
    }
}

extension View {
    /// Applies responsive content insets
    @MainActor
    func responsiveContentInsets() -> some View {
        let inset = ResponsiveDesign.LayoutAdaptation.current.contentInset
        return self.padding(.horizontal, inset)
    }
    
    /// Applies responsive spacing
    @MainActor
    func responsiveSpacing(_ size: (ResponsiveDesign.SpacingScale) -> CGFloat) -> some View {
        let spacing = size(ResponsiveDesign.SpacingScale.current)
        return self.padding(spacing)
    }
    
    /// Applies responsive corner radius
    @MainActor
    func responsiveCornerRadius() -> some View {
        let radius = ResponsiveDesign.LayoutAdaptation.current.cardCornerRadius
        return self.cornerRadius(radius)
    }
}
