/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Service for managing user preferences with type safety and observation capabilities.
*/

import Foundation
import Combine

/// Service for managing user preferences
final class UserPreferencesService: UserPreferencesServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    
    private let userDefaults: UserDefaults
    private var changeSubjects: [String: AnySubject] = [:]
    private let queue = DispatchQueue(label: "userpreferences.queue", attributes: .concurrent)
    
    // MARK: - Initialization
    
    init(userDefaults: UserDefaults = .standard) {
        self.userDefaults = userDefaults
    }
    
    // MARK: - UserPreferencesServiceProtocol
    
    func getValue<T>(for key: PreferenceKey<T>) -> T {
        return queue.sync {
            guard let value = userDefaults.object(forKey: key.key) as? T else {
                return key.defaultValue
            }
            return value
        }
    }
    
    func setValue<T>(_ value: T, for key: PreferenceKey<T>) {
        queue.async(flags: .barrier) {
            self.userDefaults.set(value, forKey: key.key)
            
            // Notify observers
            if let subject = self.changeSubjects[key.key] {
                (subject as? PassthroughSubject<T, Never>)?.send(value)
            }
        }
        
        // Update UI on main thread
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }
    
    func removeValue(for key: String) {
        queue.async(flags: .barrier) {
            self.userDefaults.removeObject(forKey: key)
        }
        
        // Update UI on main thread
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }
    
    func observeChanges<T>(for key: PreferenceKey<T>) -> AnyPublisher<T, Never> {
        return queue.sync {
            if let existingSubject = changeSubjects[key.key] as? PassthroughSubject<T, Never> {
                return existingSubject
                    .prepend(getValue(for: key))
                    .eraseToAnyPublisher()
            }
            
            let subject = PassthroughSubject<T, Never>()
            changeSubjects[key.key] = AnySubject(subject)
            
            return subject
                .prepend(getValue(for: key))
                .eraseToAnyPublisher()
        }
    }
    
    func resetToDefaults() {
        queue.async(flags: .barrier) {
            // Get all preference keys that have been set
            let allKeys = Array(self.changeSubjects.keys)
            
            // Remove all preferences
            for key in allKeys {
                self.userDefaults.removeObject(forKey: key)
            }
            
            // Clear change subjects
            self.changeSubjects.removeAll()
        }
        
        // Update UI on main thread
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }
    
    // MARK: - Convenience Methods
    
    /// Gets a boolean preference
    func getBool(for key: PreferenceKey<Bool>) -> Bool {
        return getValue(for: key)
    }
    
    /// Sets a boolean preference
    func setBool(_ value: Bool, for key: PreferenceKey<Bool>) {
        setValue(value, for: key)
    }
    
    /// Gets an integer preference
    func getInt(for key: PreferenceKey<Int>) -> Int {
        return getValue(for: key)
    }
    
    /// Sets an integer preference
    func setInt(_ value: Int, for key: PreferenceKey<Int>) {
        setValue(value, for: key)
    }
    
    /// Gets a string preference
    func getString(for key: PreferenceKey<String>) -> String {
        return getValue(for: key)
    }
    
    /// Sets a string preference
    func setString(_ value: String, for key: PreferenceKey<String>) {
        setValue(value, for: key)
    }
    
    /// Gets a double preference
    func getDouble(for key: PreferenceKey<Double>) -> Double {
        return getValue(for: key)
    }
    
    /// Sets a double preference
    func setDouble(_ value: Double, for key: PreferenceKey<Double>) {
        setValue(value, for: key)
    }
    
    // MARK: - Bulk Operations
    
    /// Sets multiple preferences at once
    func setValues(_ values: [String: Any]) {
        queue.async(flags: .barrier) {
            for (key, value) in values {
                self.userDefaults.set(value, forKey: key)
                
                // Notify observers if they exist
                if let subject = self.changeSubjects[key] {
                    (subject as? AnySubject)?.sendValue(value)
                }
            }
        }
        
        // Update UI on main thread
        DispatchQueue.main.async {
            self.objectWillChange.send()
        }
    }
    
    /// Gets all preferences as a dictionary
    func getAllPreferences() -> [String: Any] {
        return queue.sync {
            return userDefaults.dictionaryRepresentation()
        }
    }
    
    /// Exports preferences to data
    func exportPreferences() throws -> Data {
        let preferences = getAllPreferences()
        return try JSONSerialization.data(withJSONObject: preferences, options: .prettyPrinted)
    }
    
    /// Imports preferences from data
    func importPreferences(from data: Data) throws {
        let preferences = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
        guard let preferences = preferences else {
            throw UserPreferencesError.invalidImportData
        }
        
        setValues(preferences)
    }
}

// MARK: - Supporting Types

/// Type-erased subject for handling different value types
private class AnySubject {
    private let _sendValue: (Any) -> Void
    
    init<T>(_ subject: PassthroughSubject<T, Never>) {
        _sendValue = { value in
            if let typedValue = value as? T {
                subject.send(typedValue)
            }
        }
    }
    
    func sendValue(_ value: Any) {
        _sendValue(value)
    }
}

// MARK: - User Preferences Errors

enum UserPreferencesError: Error, LocalizedError {
    case invalidImportData
    case corruptedData
    case accessDenied
    
    var errorDescription: String? {
        switch self {
        case .invalidImportData:
            return "Invalid import data format"
        case .corruptedData:
            return "Preferences data is corrupted"
        case .accessDenied:
            return "Access to preferences denied"
        }
    }
}