/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Repository for managing landmark data with caching, search, and batch operations.
*/

import Foundation
import Combine
import MapKit

/// Repository for managing landmark data
final class LandmarkRepository: BaseRepository<Landmark>, SearchableRepository, BatchOperationRepository {
    
    // MARK: - Private Properties
    
    private let mapService: MapService
    private var landmarksByContinent: [AppModelData.Continent: [Landmark]] = [:]
    private var searchIndex: [String: Set<Int>] = [:]
    
    // MARK: - Initialization
    
    init(mapService: MapService = MapService(), configuration: RepositoryConfiguration = .default) {
        self.mapService = mapService
        super.init(configuration: configuration)
        setupSearchIndex()
    }
    
    // MARK: - RepositoryProtocol
    
    func fetchAll() async throws -> [Landmark] {
        updateState(.loading)
        
        do {
            // First try to load from static data (can be replaced with API call later)
            let landmarks = Landmark.exampleData
            
            // Cache landmarks
            landmarks.forEach { cacheEntity($0) }
            
            // Update internal state
            updateEntities(landmarks)
            organizeLandmarksByContinent(landmarks)
            buildSearchIndex(for: landmarks)
            
            updateState(.loaded)
            return landmarks
        } catch {
            updateState(.error(error))
            throw error
        }
    }
    
    func fetch(by id: Int) async throws -> Landmark? {
        // Try cache first
        if let cachedLandmark = getCachedEntity(by: id) {
            return cachedLandmark
        }
        
        // If not in cache, fetch from main collection
        let allLandmarks = try await fetchAll()
        return allLandmarks.first { $0.id == id }
    }
    
    func save(_ landmark: Landmark) async throws -> Landmark {
        // Update cache
        cacheEntity(landmark)
        
        // Update entities array
        var updatedEntities = entities
        if let index = updatedEntities.firstIndex(where: { $0.id == landmark.id }) {
            updatedEntities[index] = landmark
        } else {
            updatedEntities.append(landmark)
        }
        
        updateEntities(updatedEntities)
        organizeLandmarksByContinent(updatedEntities)
        buildSearchIndex(for: updatedEntities)
        
        return landmark
    }
    
    func delete(by id: Int) async throws {
        var updatedEntities = entities
        updatedEntities.removeAll { $0.id == id }
        
        updateEntities(updatedEntities)
        organizeLandmarksByContinent(updatedEntities)
        buildSearchIndex(for: updatedEntities)
    }
    
    // MARK: - SearchableRepository
    
    func search(query: String) async throws -> [Landmark] {
        guard !query.isEmpty else {
            return entities
        }
        
        let lowercaseQuery = query.lowercased()
        let matchingIds = searchIndex
            .filter { $0.key.contains(lowercaseQuery) }
            .flatMap { $0.value }
        
        let matchingLandmarks = entities.filter { matchingIds.contains($0.id) }
        return matchingLandmarks.sorted { String(localized: $0.name) < String(localized: $1.name) }
    }
    
    func filter(_ predicate: @escaping (Landmark) -> Bool) async throws -> [Landmark] {
        return entities.filter(predicate)
    }
    
    // MARK: - BatchOperationRepository
    
    func saveBatch(_ landmarks: [Landmark]) async throws -> [Landmark] {
        // Cache all landmarks
        landmarks.forEach { cacheEntity($0) }
        
        // Update entities
        var updatedEntities = entities
        for landmark in landmarks {
            if let index = updatedEntities.firstIndex(where: { $0.id == landmark.id }) {
                updatedEntities[index] = landmark
            } else {
                updatedEntities.append(landmark)
            }
        }
        
        updateEntities(updatedEntities)
        organizeLandmarksByContinent(updatedEntities)
        buildSearchIndex(for: updatedEntities)
        
        return landmarks
    }
    
    func deleteBatch(ids: [Int]) async throws {
        var updatedEntities = entities
        updatedEntities.removeAll { ids.contains($0.id) }
        
        updateEntities(updatedEntities)
        organizeLandmarksByContinent(updatedEntities)
        buildSearchIndex(for: updatedEntities)
    }
    
    // MARK: - Landmark-specific Methods
    
    /// Gets landmarks organized by continent
    func getLandmarksByContinent() -> [AppModelData.Continent: [Landmark]] {
        return landmarksByContinent
    }
    
    /// Gets landmarks for a specific continent
    func getLandmarks(for continent: AppModelData.Continent) async throws -> [Landmark] {
        let landmarks = landmarksByContinent[continent] ?? []
        return landmarks.sorted { String(localized: $0.name) < String(localized: $1.name) }
    }
    
    /// Gets featured landmark
    func getFeaturedLandmark() async throws -> Landmark? {
        // Return Mount Fuji as featured landmark (ID: 1016)
        return try await fetch(by: 1016)
    }
    
    /// Fetches map items for landmarks
    func fetchMapItems(for landmarks: [Landmark]) async throws -> [Int: MKMapItem] {
        return try await mapService.fetchMapItems(for: landmarks)
    }
    
    // MARK: - Private Methods
    
    private func organizeLandmarksByContinent(_ landmarks: [Landmark]) {
        landmarksByContinent.removeAll()
        
        for landmark in landmarks {
            guard let continent = AppModelData.Continent(rawValue: landmark.continent) else { continue }
            
            if landmarksByContinent[continent] == nil {
                landmarksByContinent[continent] = [landmark]
            } else {
                landmarksByContinent[continent]?.append(landmark)
            }
        }
    }
    
    private func setupSearchIndex() {
        buildSearchIndex(for: entities)
    }
    
    private func buildSearchIndex(for landmarks: [Landmark]) {
        searchIndex.removeAll()
        
        for landmark in landmarks {
            let searchableText = [
                String(localized: landmark.name),
                String(localized: landmark.description),
                landmark.continent,
                landmark.formattedLocation
            ].joined(separator: " ").lowercased()
            
            // Split into words and create index entries
            let words = searchableText.components(separatedBy: .whitespacesAndNewlines)
                .filter { !$0.isEmpty }
            
            for word in words {
                if searchIndex[word] == nil {
                    searchIndex[word] = Set<Int>()
                }
                searchIndex[word]?.insert(landmark.id)
            }
        }
    }
}