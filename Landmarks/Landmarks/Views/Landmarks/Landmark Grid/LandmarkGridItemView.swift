/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
A view that shows a single landmark in a grid.
*/

import SwiftUI

/// A view that shows a single landmark in a grid.
struct LandmarkGridItemView: View {
    let landmark: Landmark

    var body: some View {
        Image(landmark.thumbnailImageName)
            .resizable()
            .aspectRatio(1, contentMode: .fill)
            .overlay {
                EnhancedReadabilityOverlay(intensity: 0.8, gradientHeight: 0.5)
            }
            .clipped()
            .responsiveCornerRadius()
            .overlay(alignment: .bottom) {
                Text(landmark.name)
                    .responsiveTypographyOnImage(.subtitle)
                    .processedText(.subtitle)
                    .smartTextAlignment(for: landmark.name)
                    .padding(.bottom, Constants.standardPadding)
                    .padding(.horizontal, 8)
            }
    }
}

#Preview {
    let modelData = ModelData()
    let previewLandmark = modelData.landmarksById[1001] ?? modelData.landmarks.first!
    LandmarkGridItemView(landmark: previewLandmark)
}
