/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
The main app declaration.
*/

import SwiftUI

/// The main app declaration.
@main
struct LandmarksApp: App {
    /// An object that manages the app's data and state using the new architecture.
    @State private var appModelData = AppModelData()
    
    /// Error handler for the app.
    @State private var errorHandler = ErrorHandler()

    var body: some Scene {
        WindowGroup {
            LandmarksSplitView()
                .environment(appModelData)
                .environment(ModelData(appModelData: appModelData))
                .environment(errorHandler)
                .errorHandling(errorHandler)
                .frame(minWidth: 375.0, minHeight: 600.0)
                // Keeps the current window's size for use in scrolling header calculations.
                .onGeometryChange(for: CGSize.self) { geometry in
                    geometry.size
                } action: {
                    appModelData.windowSize = $0
                }
        }
    }
}
