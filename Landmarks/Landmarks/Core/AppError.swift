/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Unified error handling system for the app with user-friendly error messages and recovery suggestions.
*/

import Foundation
import SwiftUI

/// Unified error type for the entire application
enum AppError: Error, Identifiable, LocalizedError, Equatable {
    
    // MARK: - Data Errors
    case dataNotFound(String)
    case dataCorrupted(String)
    case dataLoadingFailed(String)
    case dataSavingFailed(String)
    
    // MARK: - Network Errors
    case networkUnavailable
    case networkTimeout
    case serverError(Int)
    case invalidResponse
    
    // MARK: - Repository Errors
    case repositoryError(String)
    case cacheError(String)
    
    // MARK: - Service Errors
    case mapServiceError(String)
    case userPreferencesError(String)
    case locationServiceError(String)
    
    // MARK: - Validation Errors
    case invalidInput(String)
    case missingRequiredField(String)
    case validationFailed(String)
    
    // MARK: - Permission Errors
    case permissionDenied(String)
    case unauthorized
    
    // MARK: - General <PERSON><PERSON><PERSON>
    case unknown(Error)
    case operationCancelled
    case featureUnavailable(String)
    
    // MARK: - Identifiable
    
    var id: String {
        switch self {
        case .dataNotFound(let details):
            return "dataNotFound_\(details)"
        case .dataCorrupted(let details):
            return "dataCorrupted_\(details)"
        case .dataLoadingFailed(let details):
            return "dataLoadingFailed_\(details)"
        case .dataSavingFailed(let details):
            return "dataSavingFailed_\(details)"
        case .networkUnavailable:
            return "networkUnavailable"
        case .networkTimeout:
            return "networkTimeout"
        case .serverError(let code):
            return "serverError_\(code)"
        case .invalidResponse:
            return "invalidResponse"
        case .repositoryError(let details):
            return "repositoryError_\(details)"
        case .cacheError(let details):
            return "cacheError_\(details)"
        case .mapServiceError(let details):
            return "mapServiceError_\(details)"
        case .userPreferencesError(let details):
            return "userPreferencesError_\(details)"
        case .locationServiceError(let details):
            return "locationServiceError_\(details)"
        case .invalidInput(let details):
            return "invalidInput_\(details)"
        case .missingRequiredField(let field):
            return "missingRequiredField_\(field)"
        case .validationFailed(let details):
            return "validationFailed_\(details)"
        case .permissionDenied(let details):
            return "permissionDenied_\(details)"
        case .unauthorized:
            return "unauthorized"
        case .unknown(let error):
            return "unknown_\(error.localizedDescription)"
        case .operationCancelled:
            return "operationCancelled"
        case .featureUnavailable(let feature):
            return "featureUnavailable_\(feature)"
        }
    }
    
    // MARK: - LocalizedError
    
    var errorDescription: String? {
        switch self {
        case .dataNotFound(let details):
            return String(localized: "Data not found: \(details)", comment: "Data not found error")
        case .dataCorrupted(let details):
            return String(localized: "Data is corrupted: \(details)", comment: "Data corrupted error")
        case .dataLoadingFailed(let details):
            return String(localized: "Failed to load data: \(details)", comment: "Data loading failed error")
        case .dataSavingFailed(let details):
            return String(localized: "Failed to save data: \(details)", comment: "Data saving failed error")
        case .networkUnavailable:
            return String(localized: "Network connection unavailable", comment: "Network unavailable error")
        case .networkTimeout:
            return String(localized: "Network request timed out", comment: "Network timeout error")
        case .serverError(let code):
            return String(localized: "Server error (code: \(code))", comment: "Server error")
        case .invalidResponse:
            return String(localized: "Invalid response from server", comment: "Invalid response error")
        case .repositoryError(let details):
            return String(localized: "Repository error: \(details)", comment: "Repository error")
        case .cacheError(let details):
            return String(localized: "Cache error: \(details)", comment: "Cache error")
        case .mapServiceError(let details):
            return String(localized: "Map service error: \(details)", comment: "Map service error")
        case .userPreferencesError(let details):
            return String(localized: "User preferences error: \(details)", comment: "User preferences error")
        case .locationServiceError(let details):
            return String(localized: "Location service error: \(details)", comment: "Location service error")
        case .invalidInput(let details):
            return String(localized: "Invalid input: \(details)", comment: "Invalid input error")
        case .missingRequiredField(let field):
            return String(localized: "Missing required field: \(field)", comment: "Missing required field error")
        case .validationFailed(let details):
            return String(localized: "Validation failed: \(details)", comment: "Validation failed error")
        case .permissionDenied(let details):
            return String(localized: "Permission denied: \(details)", comment: "Permission denied error")
        case .unauthorized:
            return String(localized: "Unauthorized access", comment: "Unauthorized error")
        case .unknown(let error):
            return String(localized: "Unknown error: \(error.localizedDescription)", comment: "Unknown error")
        case .operationCancelled:
            return String(localized: "Operation was cancelled", comment: "Operation cancelled error")
        case .featureUnavailable(let feature):
            return String(localized: "Feature unavailable: \(feature)", comment: "Feature unavailable error")
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .dataNotFound, .dataCorrupted, .dataLoadingFailed:
            return String(localized: "Please try refreshing the data or restart the app", comment: "Data error recovery suggestion")
        case .dataSavingFailed:
            return String(localized: "Please check your storage space and try again", comment: "Data saving error recovery suggestion")
        case .networkUnavailable, .networkTimeout:
            return String(localized: "Please check your internet connection and try again", comment: "Network error recovery suggestion")
        case .serverError:
            return String(localized: "Please try again later. If the problem persists, contact support", comment: "Server error recovery suggestion")
        case .invalidResponse:
            return String(localized: "Please update the app to the latest version", comment: "Invalid response recovery suggestion")
        case .repositoryError, .cacheError:
            return String(localized: "Please restart the app to reset the cache", comment: "Repository/cache error recovery suggestion")
        case .mapServiceError:
            return String(localized: "Please check your location permissions and try again", comment: "Map service error recovery suggestion")
        case .userPreferencesError:
            return String(localized: "Please reset your preferences in Settings", comment: "User preferences error recovery suggestion")
        case .locationServiceError:
            return String(localized: "Please enable location services in Settings", comment: "Location service error recovery suggestion")
        case .invalidInput, .missingRequiredField, .validationFailed:
            return String(localized: "Please check your input and try again", comment: "Input validation error recovery suggestion")
        case .permissionDenied:
            return String(localized: "Please grant the required permissions in Settings", comment: "Permission denied recovery suggestion")
        case .unauthorized:
            return String(localized: "Please sign in again", comment: "Unauthorized error recovery suggestion")
        case .operationCancelled:
            return nil // No recovery needed for cancelled operations
        case .featureUnavailable:
            return String(localized: "Please update the app to access this feature", comment: "Feature unavailable recovery suggestion")
        case .unknown:
            return String(localized: "Please restart the app and try again", comment: "Unknown error recovery suggestion")
        }
    }
    
    // MARK: - Error Severity
    
    var severity: ErrorSeverity {
        switch self {
        case .dataNotFound, .invalidInput, .missingRequiredField, .operationCancelled:
            return .low
        case .dataCorrupted, .networkTimeout, .validationFailed, .cacheError:
            return .medium
        case .dataLoadingFailed, .dataSavingFailed, .networkUnavailable, .serverError, .invalidResponse:
            return .high
        case .repositoryError, .mapServiceError, .userPreferencesError, .locationServiceError:
            return .medium
        case .permissionDenied, .unauthorized, .featureUnavailable:
            return .medium
        case .unknown:
            return .high
        }
    }
    
    // MARK: - User-facing Display
    
    var userFriendlyTitle: String {
        switch severity {
        case .low:
            return String(localized: "Information", comment: "Low severity error title")
        case .medium:
            return String(localized: "Warning", comment: "Medium severity error title")
        case .high:
            return String(localized: "Error", comment: "High severity error title")
        }
    }
    
    var shouldShowToUser: Bool {
        switch self {
        case .operationCancelled:
            return false
        default:
            return true
        }
    }
    
    // MARK: - Equatable
    
    static func == (lhs: AppError, rhs: AppError) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Error Severity

enum ErrorSeverity: Int, CaseIterable {
    case low = 1
    case medium = 2
    case high = 3
    
    var color: Color {
        switch self {
        case .low:
            return .blue
        case .medium:
            return .orange
        case .high:
            return .red
        }
    }
    
    var systemImageName: String {
        switch self {
        case .low:
            return "info.circle"
        case .medium:
            return "exclamationmark.triangle"
        case .high:
            return "xmark.circle"
        }
    }
}

// MARK: - Error Conversion Extensions

extension AppError {
    /// Creates an AppError from various error types
    static func from(_ error: Error) -> AppError {
        if let appError = error as? AppError {
            return appError
        }
        
        if let networkError = error as? NetworkServiceError {
            return convertNetworkError(networkError)
        }
        
        if let mapError = error as? MapServiceError {
            return convertMapError(mapError)
        }
        
        if let collectionError = error as? CollectionRepositoryError {
            return convertCollectionError(collectionError)
        }
        
        if let preferencesError = error as? UserPreferencesError {
            return convertPreferencesError(preferencesError)
        }
        
        return .unknown(error)
    }
    
    private static func convertNetworkError(_ error: NetworkServiceError) -> AppError {
        switch error {
        case .noConnection:
            return .networkUnavailable
        case .timeout:
            return .networkTimeout
        case .clientError(let code), .serverError(let code), .unexpectedStatusCode(let code):
            return .serverError(code)
        case .invalidResponse:
            return .invalidResponse
        case .decodingError:
            return .dataCorrupted("Response decoding failed")
        case .requestFailed(let underlyingError):
            return .unknown(underlyingError)
        }
    }
    
    private static func convertMapError(_ error: MapServiceError) -> AppError {
        switch error {
        case .missingPlaceID:
            return .dataNotFound("Place ID missing")
        case .invalidPlaceID(let placeID):
            return .invalidInput("Invalid place ID: \(placeID)")
        case .requestFailed(let underlyingError):
            return .mapServiceError(underlyingError.localizedDescription)
        case .networkUnavailable:
            return .networkUnavailable
        }
    }
    
    private static func convertCollectionError(_ error: CollectionRepositoryError) -> AppError {
        switch error {
        case .favoritesCollectionNotFound:
            return .dataNotFound("Favorites collection")
        case .cannotDeleteFavorites:
            return .validationFailed("Cannot delete favorites collection")
        case .landmarkNotFound(let id):
            return .dataNotFound("Landmark with ID \(id)")
        }
    }
    
    private static func convertPreferencesError(_ error: UserPreferencesError) -> AppError {
        switch error {
        case .invalidImportData:
            return .dataCorrupted("Preferences import data")
        case .corruptedData:
            return .dataCorrupted("User preferences")
        case .accessDenied:
            return .permissionDenied("User preferences access")
        }
    }
}