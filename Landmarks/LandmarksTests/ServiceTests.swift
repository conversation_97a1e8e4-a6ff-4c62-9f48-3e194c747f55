/*
See the LICENSE.txt file for this sample's licensing information.

Abstract:
Unit tests for Service layer implementation.
*/

import XCTest
import Combine
import Network
@testable import Landmarks

final class ServiceTests: XCTestCase {
    
    var networkService: NetworkService!
    var userPreferencesService: UserPreferencesService!
    var mapService: MapService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUpWithError() throws {
        networkService = NetworkService()
        userPreferencesService = UserPreferencesService(userDefaults: UserDefaults(suiteName: "test")!)
        mapService = MapService()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDownWithError() throws {
        networkService = nil
        userPreferencesService = nil
        mapService = nil
        cancellables = nil
    }
    
    // MARK: - NetworkService Tests
    
    func testNetworkServiceInitialization() {
        XCTAssertNotNil(networkService, "NetworkService should initialize")
        XCTAssertTrue(networkService.isConnected, "Should assume connected by default")
    }
    
    func testNetworkRequestWithInvalidURL() async {
        // Given
        let invalidURL = URL(string: "not-a-valid-url")!
        let endpoint = NetworkEndpoint(
            url: invalidURL,
            method: .GET,
            headers: nil,
            body: nil
        )
        
        // When & Then
        do {
            let _: TestResponse = try await networkService.request(endpoint, responseType: TestResponse.self)
            XCTFail("Should throw an error for invalid URL")
        } catch {
            // Expected error
            XCTAssertTrue(error is NetworkServiceError, "Should throw NetworkServiceError")
        }
    }
    
    func testCancelAllRequests() {
        // When
        networkService.cancelAllRequests()
        
        // Then - Should not crash and should be callable
        XCTAssertTrue(true, "cancelAllRequests should complete without error")
    }
    
    // MARK: - UserPreferencesService Tests
    
    func testSetAndGetBoolPreference() {
        // Given
        let key = PreferenceKey("testBool", defaultValue: false)
        
        // When
        userPreferencesService.setValue(true, for: key)
        let value = userPreferencesService.getValue(for: key)
        
        // Then
        XCTAssertTrue(value, "Should return the set boolean value")
    }
    
    func testSetAndGetIntPreference() {
        // Given
        let key = PreferenceKey("testInt", defaultValue: 0)
        
        // When
        userPreferencesService.setValue(42, for: key)
        let value = userPreferencesService.getValue(for: key)
        
        // Then
        XCTAssertEqual(value, 42, "Should return the set integer value")
    }
    
    func testSetAndGetStringPreference() {
        // Given
        let key = PreferenceKey("testString", defaultValue: "default")
        
        // When
        userPreferencesService.setValue("test value", for: key)
        let value = userPreferencesService.getValue(for: key)
        
        // Then
        XCTAssertEqual(value, "test value", "Should return the set string value")
    }
    
    func testDefaultValueForUnsetPreference() {
        // Given
        let key = PreferenceKey("unsetKey", defaultValue: "default")
        
        // When
        let value = userPreferencesService.getValue(for: key)
        
        // Then
        XCTAssertEqual(value, "default", "Should return default value for unset preference")
    }
    
    func testRemovePreference() {
        // Given
        let key = PreferenceKey("testRemove", defaultValue: "default")
        userPreferencesService.setValue("set value", for: key)
        
        // When
        userPreferencesService.removeValue(for: key.key)
        let value = userPreferencesService.getValue(for: key)
        
        // Then
        XCTAssertEqual(value, "default", "Should return default value after removal")
    }
    
    func testObservePreferenceChanges() {
        // Given
        let key = PreferenceKey("testObserve", defaultValue: 0)
        let expectation = XCTestExpectation(description: "Preference change observed")
        var observedValues: [Int] = []
        
        // When
        userPreferencesService.observeChanges(for: key)
            .sink { value in
                observedValues.append(value)
                if observedValues.count >= 3 { // Initial + 2 changes
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        userPreferencesService.setValue(1, for: key)
        userPreferencesService.setValue(2, for: key)
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(observedValues, [0, 1, 2], "Should observe all preference changes")
    }
    
    func testResetToDefaults() {
        // Given
        let key1 = PreferenceKey("test1", defaultValue: "default1")
        let key2 = PreferenceKey("test2", defaultValue: "default2")
        
        userPreferencesService.setValue("changed1", for: key1)
        userPreferencesService.setValue("changed2", for: key2)
        
        // When
        userPreferencesService.resetToDefaults()
        
        // Then
        XCTAssertEqual(userPreferencesService.getValue(for: key1), "default1", "Should reset to default")
        XCTAssertEqual(userPreferencesService.getValue(for: key2), "default2", "Should reset to default")
    }
    
    func testBulkOperations() {
        // Given
        let values = [
            "key1": "value1",
            "key2": 42,
            "key3": true
        ] as [String: Any]
        
        // When
        userPreferencesService.setValues(values)
        
        // Then
        let key1 = PreferenceKey("key1", defaultValue: "")
        let key2 = PreferenceKey("key2", defaultValue: 0)
        let key3 = PreferenceKey("key3", defaultValue: false)
        
        XCTAssertEqual(userPreferencesService.getValue(for: key1), "value1")
        XCTAssertEqual(userPreferencesService.getValue(for: key2), 42)
        XCTAssertEqual(userPreferencesService.getValue(for: key3), true)
    }
    
    func testExportImportPreferences() throws {
        // Given
        let key1 = PreferenceKey("export1", defaultValue: "default1")
        let key2 = PreferenceKey("export2", defaultValue: 0)
        
        userPreferencesService.setValue("exported1", for: key1)
        userPreferencesService.setValue(99, for: key2)
        
        // When - Export
        let exportedData = try userPreferencesService.exportPreferences()
        
        // Reset preferences
        userPreferencesService.resetToDefaults()
        
        // Import back
        try userPreferencesService.importPreferences(from: exportedData)
        
        // Then
        XCTAssertEqual(userPreferencesService.getValue(for: key1), "exported1")
        XCTAssertEqual(userPreferencesService.getValue(for: key2), 99)
    }
    
    // MARK: - MapService Tests
    
    func testMapServiceInitialization() {
        XCTAssertNotNil(mapService, "MapService should initialize")
    }
    
    func testCleanupCache() {
        // When
        mapService.cleanupCache()
        
        // Then - Should not crash
        XCTAssertTrue(true, "cleanupCache should complete without error")
    }
    
    func testFetchMapItemsWithEmptyArray() async throws {
        // Given
        let emptyLandmarks: [Landmark] = []
        
        // When
        let result = try await mapService.fetchMapItems(for: emptyLandmarks)
        
        // Then
        XCTAssertTrue(result.isEmpty, "Should return empty result for empty input")
    }
    
    // MARK: - Performance Tests
    
    func testPreferencesPerformance() {
        let key = PreferenceKey("perfTest", defaultValue: 0)
        
        measure {
            for i in 0..<1000 {
                userPreferencesService.setValue(i, for: key)
                _ = userPreferencesService.getValue(for: key)
            }
        }
    }
    
    func testNetworkServicePerformance() {
        measure {
            for _ in 0..<100 {
                networkService.cancelAllRequests()
            }
        }
    }
}

// MARK: - Test-specific Types

private struct TestResponse: Codable {
    let message: String
    let status: Int
}